package ${packageName};

#foreach ($importClass in $importPackageList)
$importClass
#end
import cn.edu.xmut.tsa.base.common.domain.ResponseDTO;
import cn.edu.xmut.tsa.base.common.domain.PageResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;

/**
 * ${basic.description} Controller
 *
 * <AUTHOR>
 * @Date ${basic.backendDate}
 * @Copyright ${basic.copyright}
 */

@RestController
@Tag(name = "${basic.description}")
public class ${name.upperCamel}Controller {

    @Resource
    private ${name.upperCamel}Service ${name.lowerCamel}Service;

    @Operation(summary = "分页查询 <AUTHOR>
    @PostMapping("/${name.lowerCamel}/queryPage")
    @SaCheckPermission("${name.lowerCamel}:query")
    public ResponseDTO<PageResult<${name.upperCamel}VO>> queryPage(@RequestBody @Valid ${name.upperCamel}QueryForm queryForm) {
        return ResponseDTO.ok(${name.lowerCamel}Service.queryPage(queryForm));
    }

#if($insertAndUpdate.isSupportInsertAndUpdate)
    @Operation(summary = "添加 <AUTHOR>
    @PostMapping("/${name.lowerCamel}/add")
    @SaCheckPermission("${name.lowerCamel}:add")
    public ResponseDTO<String> add(@RequestBody @Valid ${name.upperCamel}AddForm addForm) {
        return ${name.lowerCamel}Service.add(addForm);
    }

    @Operation(summary = "更新 <AUTHOR>
    @PostMapping("/${name.lowerCamel}/update")
    @SaCheckPermission("${name.lowerCamel}:update")
    public ResponseDTO<String> update(@RequestBody @Valid ${name.upperCamel}UpdateForm updateForm) {
        return ${name.lowerCamel}Service.update(updateForm);
    }
#end

#if($deleteInfo.isSupportDelete)
    #if($deleteInfo.deleteEnum == "Batch" || $deleteInfo.deleteEnum == "SingleAndBatch")
    @Operation(summary = "批量删除 <AUTHOR>
    @PostMapping("/${name.lowerCamel}/batchDelete")
    @SaCheckPermission("${name.lowerCamel}:delete")
    public ResponseDTO<String> batchDelete(@RequestBody ValidateList<${primaryKeyJavaType}> idList) {
        return ${name.lowerCamel}Service.batchDelete(idList);
    }
    #end

    #if($deleteInfo.deleteEnum == "Single" || $deleteInfo.deleteEnum == "SingleAndBatch")
    @Operation(summary = "单个删除 <AUTHOR>
    @GetMapping("/${name.lowerCamel}/delete/{${primaryKeyFieldName}}")
    @SaCheckPermission("${name.lowerCamel}:delete")
    public ResponseDTO<String> batchDelete(@PathVariable ${primaryKeyJavaType} ${primaryKeyFieldName}) {
        return ${name.lowerCamel}Service.delete(${primaryKeyFieldName});
    }
    #end
#end
}
