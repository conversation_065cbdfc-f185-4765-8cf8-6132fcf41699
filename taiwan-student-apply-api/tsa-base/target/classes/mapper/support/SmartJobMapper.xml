<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.edu.xmut.tsa.base.module.support.job.repository.SmartJobDao">

    <update id="updateDeletedFlag">
        update t_smart_job
        set deleted_flag = #{deletedFlag}
        where job_id = #{jobId}
    </update>

    <!-- 定时任务-分页查询 -->
    <select id="query" resultType="cn.edu.xmut.tsa.base.module.support.job.api.domain.SmartJobVO">
        SELECT *
        FROM t_smart_job
        <where>
            <if test="query.searchWord != null and query.searchWord != ''">
                AND ( INSTR(job_name,#{query.searchWord})
                OR INSTR(job_class,#{query.searchWord})
                OR INSTR(trigger_value,#{query.searchWord})
                )
            </if>
            <if test="query.triggerType != null">
                AND trigger_type = #{query.triggerType}
            </if>
            <if test="query.enabledFlag != null">
                AND enabled_flag = #{query.enabledFlag}
            </if>
            <if test="query.deletedFlag != null">
                AND deleted_flag = #{query.deletedFlag}
            </if>
        </where>
        <if test="query.sortItemList == null or query.sortItemList.size == 0">
            ORDER BY sort ASC,job_id DESC
        </if>
    </select>

    <select id="selectByJobClass" resultType="cn.edu.xmut.tsa.base.module.support.job.repository.domain.SmartJobEntity">
        SELECT *
        FROM t_smart_job
        WHERE job_class = #{jobClass}
    </select>

</mapper>