package cn.edu.xmut.tsa.admin.module.enrollment.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.*;

/**
 * 招生计划更新表单
 *
 * <AUTHOR>
 * @Date 2025-06-20
 */
@Data
@Schema(description = "招生计划更新表单")
public class EnrollmentPlanUpdateForm {

    /**
     * 招生计划ID
     */
    @Schema(description = "招生计划ID")
    @NotNull(message = "招生计划ID不能为空")
    private Long enrollmentPlanId;

    /**
     * 招生年度
     */
    @Schema(description = "招生年度")
    @NotNull(message = "招生年度不能为空")
    @Min(value = 2000, message = "招生年度不能小于2000年")
    @Max(value = 2100, message = "招生年度不能大于2100年")
    private Integer year;

    /**
     * 科类
     */
    @Schema(description = "科类")
    @NotNull(message = "科类不能为空")
    private Integer category;

    /**
     * 专业名称
     */
    @Schema(description = "专业名称")
    @NotBlank(message = "专业名称不能为空")
    @Size(max = 200, message = "专业名称长度不能超过200个字符")
    private String majorName;

    /**
     * 所属学院
     */
    @Schema(description = "所属学院")
    @NotBlank(message = "所属学院不能为空")
    @Size(max = 200, message = "所属学院长度不能超过200个字符")
    private String collegeName;

    /**
     * 学制（年）
     */
    @Schema(description = "学制（年）")
    @NotNull(message = "学制不能为空")
    @Min(value = 1, message = "学制不能小于1年")
    @Max(value = 8, message = "学制不能大于8年")
    private Integer educationDuration;

    /**
     * 招生人数
     */
    @Schema(description = "招生人数")
    @NotNull(message = "招生人数不能为空")
    @Min(value = 0, message = "招生人数不能小于0")
    @Max(value = 9999, message = "招生人数不能大于9999")
    private Integer enrollmentCount;

    /**
     * 备注
     */
    @Schema(description = "备注")
    @Size(max = 500, message = "备注长度不能超过500个字符")
    private String remark;

    /**
     * 排序
     */
    @Schema(description = "排序")
    @Min(value = 0, message = "排序不能小于0")
    private Integer sort;
}
