package cn.edu.xmut.tsa.admin.module.enrollment.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 招生计划年度复制表单
 *
 * <AUTHOR>
 * @Date 2025-06-20
 */
@Data
@Schema(description = "招生计划年度复制表单")
public class EnrollmentPlanYearCopyForm {

    /**
     * 源年度
     */
    @Schema(description = "源年度")
    @NotNull(message = "源年度不能为空")
    @Min(value = 2000, message = "源年度不能小于2000年")
    @Max(value = 2100, message = "源年度不能大于2100年")
    private Integer fromYear;

    /**
     * 目标年度
     */
    @Schema(description = "目标年度")
    @NotNull(message = "目标年度不能为空")
    @Min(value = 2000, message = "目标年度不能小于2000年")
    @Max(value = 2100, message = "目标年度不能大于2100年")
    private Integer toYear;
}
