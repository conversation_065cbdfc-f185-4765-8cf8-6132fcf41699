package cn.edu.xmut.tsa.admin.module.enrollment.manager;

import cn.edu.xmut.tsa.admin.module.enrollment.dao.EnrollmentPlanDao;
import cn.edu.xmut.tsa.admin.module.enrollment.domain.entity.EnrollmentPlanEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * 招生计划 Manager
 *
 * <AUTHOR>
 * @Date 2025-06-20
 */
@Service
public class EnrollmentPlanManager {

    @Resource
    private EnrollmentPlanDao enrollmentPlanDao;

    /**
     * 批量删除招生计划
     */
    @Transactional(rollbackFor = Exception.class)
    public void batchDelete(List<Long> idList) {
        enrollmentPlanDao.deleteBatchIds(idList);
    }

    /**
     * 年度复制
     */
    @Transactional(rollbackFor = Exception.class)
    public void copyFromPreviousYear(List<EnrollmentPlanEntity> sourceList, Integer toYear) {
        // 设置新年度并清空ID
        for (EnrollmentPlanEntity entity : sourceList) {
            entity.setEnrollmentPlanId(null);
            entity.setYear(toYear);
            entity.setCreateTime(null);
            entity.setUpdateTime(null);
        }
        
        // 批量插入
        if (!sourceList.isEmpty()) {
            enrollmentPlanDao.batchInsert(sourceList);
        }
    }

    /**
     * 批量导入招生计划
     */
    @Transactional(rollbackFor = Exception.class)
    public void batchImport(List<EnrollmentPlanEntity> importList) {
        if (!importList.isEmpty()) {
            enrollmentPlanDao.batchInsert(importList);
        }
    }
}
