package cn.edu.xmut.tsa.admin.module.enrollment.domain.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 招生计划导入视图对象
 *
 * <AUTHOR>
 * @Date 2025-06-20
 */
@Data
public class EnrollmentPlanImportVO {

    /**
     * 科类名称
     */
    @ExcelProperty("科类")
    private String categoryName;

    /**
     * 专业名称
     */
    @ExcelProperty("专业名称")
    private String majorName;

    /**
     * 所属学院
     */
    @ExcelProperty("所属学院")
    private String collegeName;

    /**
     * 学制（年）
     */
    @ExcelProperty("学制（年）")
    private Integer educationDuration;

    /**
     * 招生人数
     */
    @ExcelProperty("招生人数")
    private Integer enrollmentCount;

    /**
     * 备注
     */
    @ExcelProperty("备注")
    private String remark;
}
