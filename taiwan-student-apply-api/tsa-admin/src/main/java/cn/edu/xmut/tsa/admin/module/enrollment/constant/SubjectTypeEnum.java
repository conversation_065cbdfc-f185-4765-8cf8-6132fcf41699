package cn.edu.xmut.tsa.admin.module.enrollment.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 科类枚举
 *
 * <AUTHOR>
 * @Date 2025-06-20
 */
@Getter
@AllArgsConstructor
public enum SubjectTypeEnum {

    /**
     * 文史科
     */
    LIBERAL_ARTS(1, "文史科"),

    /**
     * 理工科
     */
    SCIENCE_ENGINEERING(5, "理工科");

    private final Integer value;
    private final String desc;

    /**
     * 根据值获取枚举
     */
    public static SubjectTypeEnum getByValue(Integer value) {
        if (value == null) {
            return null;
        }
        for (SubjectTypeEnum subjectType : SubjectTypeEnum.values()) {
            if (subjectType.getValue().equals(value)) {
                return subjectType;
            }
        }
        return null;
    }

    /**
     * 根据描述获取枚举
     */
    public static SubjectTypeEnum getByDesc(String desc) {
        if (desc == null || desc.trim().isEmpty()) {
            return null;
        }
        for (SubjectTypeEnum subjectType : SubjectTypeEnum.values()) {
            if (subjectType.getDesc().equals(desc.trim())) {
                return subjectType;
            }
        }
        return null;
    }

    /**
     * 校验值是否有效
     */
    public static boolean isValid(Integer value) {
        return getByValue(value) != null;
    }

    /**
     * 校验描述是否有效
     */
    public static boolean isValidDesc(String desc) {
        return getByDesc(desc) != null;
    }
}
