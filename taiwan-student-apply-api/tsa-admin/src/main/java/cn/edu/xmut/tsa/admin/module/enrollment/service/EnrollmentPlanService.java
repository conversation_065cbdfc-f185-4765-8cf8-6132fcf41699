package cn.edu.xmut.tsa.admin.module.enrollment.service;

import cn.edu.xmut.tsa.admin.module.enrollment.constant.SubjectTypeEnum;
import cn.edu.xmut.tsa.admin.module.enrollment.dao.EnrollmentPlanDao;
import cn.edu.xmut.tsa.admin.module.enrollment.domain.entity.EnrollmentPlanEntity;
import cn.edu.xmut.tsa.admin.module.enrollment.domain.form.*;
import cn.edu.xmut.tsa.admin.module.enrollment.domain.vo.EnrollmentPlanExportVO;
import cn.edu.xmut.tsa.admin.module.enrollment.domain.vo.EnrollmentPlanImportVO;
import cn.edu.xmut.tsa.admin.module.enrollment.domain.vo.EnrollmentPlanVO;
import cn.edu.xmut.tsa.admin.module.enrollment.manager.EnrollmentPlanManager;
import cn.edu.xmut.tsa.base.common.domain.PageResult;
import cn.edu.xmut.tsa.base.common.domain.RequestUser;
import cn.edu.xmut.tsa.base.common.domain.ResponseDTO;
import cn.edu.xmut.tsa.base.common.util.SmartBeanUtil;
import cn.edu.xmut.tsa.base.common.util.SmartExcelUtil;
import cn.edu.xmut.tsa.base.common.util.SmartPageUtil;
import cn.edu.xmut.tsa.base.common.util.SmartResponseUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 招生计划 Service
 *
 * <AUTHOR>
 * @Date 2025-06-20
 */
@Slf4j
@Service
public class EnrollmentPlanService {

    @Resource
    private EnrollmentPlanDao enrollmentPlanDao;

    @Resource
    private EnrollmentPlanManager enrollmentPlanManager;

    /**
     * 分页查询
     */
    public PageResult<EnrollmentPlanVO> queryPage(EnrollmentPlanQueryForm queryForm) {
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<EnrollmentPlanVO> list = enrollmentPlanDao.queryPage(page, queryForm);
        
        // 设置科类名称
        list.forEach(this::setCategoryName);
        
        return SmartPageUtil.convert2PageResult(page, list);
    }

    /**
     * 新增招生计划
     */
    public ResponseDTO<String> add(EnrollmentPlanAddForm addForm, RequestUser requestUser) {
        // 校验科类是否有效
        if (!SubjectTypeEnum.isValid(addForm.getCategory())) {
            return ResponseDTO.userErrorParam("科类参数无效");
        }

        // 检查同年度同专业是否已存在
        Long count = enrollmentPlanDao.checkMajorExists(addForm.getYear(), addForm.getMajorName(), addForm.getCollegeName());
        if (count > 0) {
            return ResponseDTO.userErrorParam("该年度该学院的专业已存在");
        }

        EnrollmentPlanEntity entity = SmartBeanUtil.copy(addForm, EnrollmentPlanEntity.class);
        entity.setDeletedFlag(false);
        entity.setCreateTime(LocalDateTime.now());
        entity.setUpdateTime(LocalDateTime.now());
        
        if (entity.getSort() == null) {
            entity.setSort(0);
        }

        enrollmentPlanDao.insert(entity);
        return ResponseDTO.ok();
    }

    /**
     * 更新招生计划
     */
    public ResponseDTO<String> update(EnrollmentPlanUpdateForm updateForm, RequestUser requestUser) {
        // 校验科类是否有效
        if (!SubjectTypeEnum.isValid(updateForm.getCategory())) {
            return ResponseDTO.userErrorParam("科类参数无效");
        }

        // 检查记录是否存在
        EnrollmentPlanEntity existEntity = enrollmentPlanDao.selectById(updateForm.getEnrollmentPlanId());
        if (existEntity == null || existEntity.getDeletedFlag()) {
            return ResponseDTO.userErrorParam("招生计划不存在");
        }

        // 检查同年度同专业是否已存在（排除自己）
        Long count = enrollmentPlanDao.checkMajorExists(updateForm.getYear(), updateForm.getMajorName(), updateForm.getCollegeName());
        if (count > 0 && !existEntity.getMajorName().equals(updateForm.getMajorName()) || !existEntity.getCollegeName().equals(updateForm.getCollegeName())) {
            return ResponseDTO.userErrorParam("该年度该学院的专业已存在");
        }

        EnrollmentPlanEntity entity = SmartBeanUtil.copy(updateForm, EnrollmentPlanEntity.class);
        entity.setUpdateTime(LocalDateTime.now());

        enrollmentPlanDao.updateById(entity);
        return ResponseDTO.ok();
    }

    /**
     * 删除招生计划
     */
    public ResponseDTO<String> delete(Long enrollmentPlanId) {
        EnrollmentPlanEntity entity = enrollmentPlanDao.selectById(enrollmentPlanId);
        if (entity == null || entity.getDeletedFlag()) {
            return ResponseDTO.userErrorParam("招生计划不存在");
        }

        entity.setDeletedFlag(true);
        entity.setUpdateTime(LocalDateTime.now());
        enrollmentPlanDao.updateById(entity);
        
        return ResponseDTO.ok();
    }

    /**
     * 批量删除招生计划
     */
    public ResponseDTO<String> batchDelete(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return ResponseDTO.userErrorParam("请选择要删除的记录");
        }

        // 批量更新删除标志
        List<EnrollmentPlanEntity> updateList = idList.stream().map(id -> {
            EnrollmentPlanEntity entity = new EnrollmentPlanEntity();
            entity.setEnrollmentPlanId(id);
            entity.setDeletedFlag(true);
            entity.setUpdateTime(LocalDateTime.now());
            return entity;
        }).collect(Collectors.toList());

        updateList.forEach(entity -> enrollmentPlanDao.updateById(entity));
        
        return ResponseDTO.ok();
    }

    /**
     * 查询招生计划详情
     */
    public ResponseDTO<EnrollmentPlanVO> getDetail(Long enrollmentPlanId) {
        EnrollmentPlanEntity entity = enrollmentPlanDao.selectById(enrollmentPlanId);
        if (entity == null || entity.getDeletedFlag()) {
            return ResponseDTO.userErrorParam("招生计划不存在");
        }

        EnrollmentPlanVO vo = SmartBeanUtil.copy(entity, EnrollmentPlanVO.class);
        setCategoryName(vo);
        
        return ResponseDTO.ok(vo);
    }

    /**
     * 查询所有年度列表
     */
    public ResponseDTO<List<Integer>> queryYearList() {
        List<Integer> yearList = enrollmentPlanDao.queryYearList();
        return ResponseDTO.ok(yearList);
    }

    /**
     * 根据年度统计招生计划数量
     */
    public ResponseDTO<Map<Integer, Integer>> countByYear() {
        List<EnrollmentPlanVO> countList = enrollmentPlanDao.countByYear();
        Map<Integer, Integer> countMap = countList.stream()
                .collect(Collectors.toMap(EnrollmentPlanVO::getYear, EnrollmentPlanVO::getEnrollmentCount));
        return ResponseDTO.ok(countMap);
    }

    /**
     * 年度复制
     */
    public ResponseDTO<String> copyFromPreviousYear(EnrollmentPlanYearCopyForm copyForm, RequestUser requestUser) {
        if (copyForm.getFromYear().equals(copyForm.getToYear())) {
            return ResponseDTO.userErrorParam("源年度和目标年度不能相同");
        }

        // 检查源年度是否有数据
        List<EnrollmentPlanEntity> sourceList = enrollmentPlanDao.queryByYearEntity(copyForm.getFromYear());
        if (CollectionUtils.isEmpty(sourceList)) {
            return ResponseDTO.userErrorParam("源年度没有招生计划数据");
        }

        // 检查目标年度是否已有数据
        List<EnrollmentPlanEntity> targetList = enrollmentPlanDao.queryByYearEntity(copyForm.getToYear());
        if (!CollectionUtils.isEmpty(targetList)) {
            return ResponseDTO.userErrorParam("目标年度已存在招生计划数据，请先清空");
        }

        // 执行复制
        enrollmentPlanManager.copyFromPreviousYear(sourceList, copyForm.getToYear());

        return ResponseDTO.ok("复制成功，共复制 " + sourceList.size() + " 条记录");
    }

    /**
     * 导出Excel
     */
    public void exportExcel(EnrollmentPlanQueryForm queryForm, HttpServletResponse response) throws IOException {
        List<EnrollmentPlanVO> list = enrollmentPlanDao.queryList(queryForm);

        // 转换为导出VO
        List<EnrollmentPlanExportVO> exportList = list.stream().map(vo -> {
            EnrollmentPlanExportVO exportVO = SmartBeanUtil.copy(vo, EnrollmentPlanExportVO.class);
            // 设置科类名称
            SubjectTypeEnum subjectType = SubjectTypeEnum.getByValue(vo.getCategory());
            if (subjectType != null) {
                exportVO.setCategoryName(subjectType.getDesc());
            }
            return exportVO;
        }).collect(Collectors.toList());

        String fileName = "招生计划_" + (queryForm.getYear() != null ? queryForm.getYear() + "年_" : "") + System.currentTimeMillis() + ".xlsx";
        SmartExcelUtil.exportExcel(response, fileName, "招生计划", EnrollmentPlanExportVO.class, exportList);
    }

    /**
     * 下载导入模板
     */
    public void downloadTemplate(HttpServletResponse response) throws IOException {
        // 创建模板数据
        List<EnrollmentPlanImportVO> templateList = Arrays.asList(
            createTemplateData("理工科", "计算机科学与技术", "计算机科学学院", 4, 100, "示例数据1"),
            createTemplateData("文史科", "汉语言文学", "文学院", 4, 80, "示例数据2")
        );

        String fileName = "招生计划导入模板_" + System.currentTimeMillis() + ".xlsx";
        SmartExcelUtil.exportExcel(response, fileName, "招生计划导入模板", EnrollmentPlanImportVO.class, templateList);
    }

    /**
     * 导入Excel
     */
    public ResponseDTO<String> importExcel(MultipartFile file, Integer year, RequestUser requestUser) {
        if (file == null || file.isEmpty()) {
            return ResponseDTO.userErrorParam("请选择要导入的文件");
        }

        if (year == null) {
            return ResponseDTO.userErrorParam("请选择导入年度");
        }

        try {
            // 读取Excel数据
            List<EnrollmentPlanImportVO> importList = EasyExcel.read(file.getInputStream())
                    .head(EnrollmentPlanImportVO.class)
                    .sheet()
                    .doReadSync();

            if (CollectionUtils.isEmpty(importList)) {
                return ResponseDTO.userErrorParam("导入文件为空");
            }

            // 数据校验和转换
            List<String> errorMessages = new ArrayList<>();
            List<EnrollmentPlanEntity> entityList = new ArrayList<>();
            Set<String> duplicateCheck = new HashSet<>();

            for (int i = 0; i < importList.size(); i++) {
                EnrollmentPlanImportVO importVO = importList.get(i);
                int rowNum = i + 2; // Excel行号从2开始（第1行是标题）

                // 校验数据
                List<String> rowErrors = validateImportData(importVO, year, rowNum, duplicateCheck);
                if (!rowErrors.isEmpty()) {
                    errorMessages.addAll(rowErrors);
                    continue;
                }

                // 转换为实体
                EnrollmentPlanEntity entity = convertImportToEntity(importVO, year);
                entityList.add(entity);
            }

            if (!errorMessages.isEmpty()) {
                return ResponseDTO.userErrorParam("数据校验失败：\n" + String.join("\n", errorMessages));
            }

            // 批量导入
            enrollmentPlanManager.batchImport(entityList);

            return ResponseDTO.ok("导入成功，共导入 " + entityList.size() + " 条记录");

        } catch (Exception e) {
            log.error("导入Excel失败", e);
            return ResponseDTO.userErrorParam("导入失败：" + e.getMessage());
        }
    }

    /**
     * 创建模板数据
     */
    private EnrollmentPlanImportVO createTemplateData(String categoryName, String majorName, String collegeName,
                                                     Integer educationDuration, Integer enrollmentCount, String remark) {
        EnrollmentPlanImportVO template = new EnrollmentPlanImportVO();
        template.setCategoryName(categoryName);
        template.setMajorName(majorName);
        template.setCollegeName(collegeName);
        template.setEducationDuration(educationDuration);
        template.setEnrollmentCount(enrollmentCount);
        template.setRemark(remark);
        return template;
    }

    /**
     * 校验导入数据
     */
    private List<String> validateImportData(EnrollmentPlanImportVO importVO, Integer year, int rowNum, Set<String> duplicateCheck) {
        List<String> errors = new ArrayList<>();

        // 必填字段校验
        if (importVO.getCategoryName() == null || importVO.getCategoryName().trim().isEmpty()) {
            errors.add("第" + rowNum + "行：科类不能为空");
        }
        if (importVO.getMajorName() == null || importVO.getMajorName().trim().isEmpty()) {
            errors.add("第" + rowNum + "行：专业名称不能为空");
        }
        if (importVO.getCollegeName() == null || importVO.getCollegeName().trim().isEmpty()) {
            errors.add("第" + rowNum + "行：所属学院不能为空");
        }
        if (importVO.getEducationDuration() == null) {
            errors.add("第" + rowNum + "行：学制不能为空");
        }
        if (importVO.getEnrollmentCount() == null) {
            errors.add("第" + rowNum + "行：招生人数不能为空");
        }

        // 科类校验
        if (importVO.getCategoryName() != null && !SubjectTypeEnum.isValidDesc(importVO.getCategoryName().trim())) {
            errors.add("第" + rowNum + "行：科类值无效，只支持：文史科、理工科");
        }

        // 学制校验
        if (importVO.getEducationDuration() != null && (importVO.getEducationDuration() < 1 || importVO.getEducationDuration() > 8)) {
            errors.add("第" + rowNum + "行：学制必须在1-8年之间");
        }

        // 招生人数校验
        if (importVO.getEnrollmentCount() != null && (importVO.getEnrollmentCount() < 0 || importVO.getEnrollmentCount() > 9999)) {
            errors.add("第" + rowNum + "行：招生人数必须在0-9999之间");
        }

        // 长度校验
        if (importVO.getMajorName() != null && importVO.getMajorName().length() > 200) {
            errors.add("第" + rowNum + "行：专业名称长度不能超过200个字符");
        }
        if (importVO.getCollegeName() != null && importVO.getCollegeName().length() > 200) {
            errors.add("第" + rowNum + "行：所属学院长度不能超过200个字符");
        }
        if (importVO.getRemark() != null && importVO.getRemark().length() > 500) {
            errors.add("第" + rowNum + "行：备注长度不能超过500个字符");
        }

        // 重复性校验（文件内部）
        if (importVO.getMajorName() != null && importVO.getCollegeName() != null) {
            String key = importVO.getMajorName().trim() + "|" + importVO.getCollegeName().trim();
            if (duplicateCheck.contains(key)) {
                errors.add("第" + rowNum + "行：专业名称和学院组合在导入文件中重复");
            } else {
                duplicateCheck.add(key);
            }

            // 数据库重复性校验
            if (errors.isEmpty()) {
                Long count = enrollmentPlanDao.checkMajorExists(year, importVO.getMajorName().trim(), importVO.getCollegeName().trim());
                if (count > 0) {
                    errors.add("第" + rowNum + "行：该年度该学院的专业已存在于数据库中");
                }
            }
        }

        return errors;
    }

    /**
     * 转换导入数据为实体
     */
    private EnrollmentPlanEntity convertImportToEntity(EnrollmentPlanImportVO importVO, Integer year) {
        EnrollmentPlanEntity entity = new EnrollmentPlanEntity();
        entity.setYear(year);

        // 转换科类
        SubjectTypeEnum subjectType = SubjectTypeEnum.getByDesc(importVO.getCategoryName().trim());
        entity.setCategory(subjectType.getValue());

        entity.setMajorName(importVO.getMajorName().trim());
        entity.setCollegeName(importVO.getCollegeName().trim());
        entity.setEducationDuration(importVO.getEducationDuration());
        entity.setEnrollmentCount(importVO.getEnrollmentCount());
        entity.setRemark(importVO.getRemark() != null ? importVO.getRemark().trim() : null);
        entity.setSort(0);
        entity.setDeletedFlag(false);
        entity.setCreateTime(LocalDateTime.now());
        entity.setUpdateTime(LocalDateTime.now());

        return entity;
    }

    /**
     * 设置科类名称
     */
    private void setCategoryName(EnrollmentPlanVO vo) {
        SubjectTypeEnum subjectType = SubjectTypeEnum.getByValue(vo.getCategory());
        if (subjectType != null) {
            vo.setCategoryName(subjectType.getDesc());
        }
    }
}
