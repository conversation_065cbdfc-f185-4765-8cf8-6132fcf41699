package cn.edu.xmut.tsa.admin.module.enrollment.domain.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

/**
 * 招生计划导出视图对象
 *
 * <AUTHOR>
 * @Date 2025-06-20
 */
@Data
@ContentRowHeight(20)
@HeadRowHeight(25)
public class EnrollmentPlanExportVO {

    /**
     * 招生年度
     */
    @ExcelProperty("招生年度")
    @ColumnWidth(12)
    private Integer year;

    /**
     * 科类名称
     */
    @ExcelProperty("科类")
    @ColumnWidth(10)
    private String categoryName;

    /**
     * 专业名称
     */
    @ExcelProperty("专业名称")
    @ColumnWidth(25)
    private String majorName;

    /**
     * 所属学院
     */
    @ExcelProperty("所属学院")
    @ColumnWidth(20)
    private String collegeName;

    /**
     * 学制（年）
     */
    @ExcelProperty("学制（年）")
    @ColumnWidth(12)
    private Integer educationDuration;

    /**
     * 招生人数
     */
    @ExcelProperty("招生人数")
    @ColumnWidth(12)
    private Integer enrollmentCount;

    /**
     * 备注
     */
    @ExcelProperty("备注")
    @ColumnWidth(30)
    private String remark;
}
