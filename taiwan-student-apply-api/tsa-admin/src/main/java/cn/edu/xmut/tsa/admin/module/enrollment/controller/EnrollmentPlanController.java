package cn.edu.xmut.tsa.admin.module.enrollment.controller;

import cn.edu.xmut.tsa.admin.module.enrollment.domain.form.*;
import cn.edu.xmut.tsa.admin.module.enrollment.domain.vo.EnrollmentPlanVO;
import cn.edu.xmut.tsa.admin.module.enrollment.service.EnrollmentPlanService;
import cn.edu.xmut.tsa.base.common.domain.PageResult;
import cn.edu.xmut.tsa.base.common.domain.ResponseDTO;
import cn.edu.xmut.tsa.base.common.util.SmartRequestUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 招生计划 Controller
 *
 * <AUTHOR>
 * @Date 2025-06-20
 */
@RestController
@Tag(name = "招生计划管理")
@RequestMapping("/enrollmentPlan")
public class EnrollmentPlanController {

    @Resource
    private EnrollmentPlanService enrollmentPlanService;

    @Operation(summary = "分页查询招生计划 <AUTHOR>
    @PostMapping("/queryPage")
    public ResponseDTO<PageResult<EnrollmentPlanVO>> queryPage(@RequestBody @Valid EnrollmentPlanQueryForm queryForm) {
        return ResponseDTO.ok(enrollmentPlanService.queryPage(queryForm));
    }

    @Operation(summary = "新增招生计划 <AUTHOR>
    @PostMapping("/add")
    public ResponseDTO<String> add(@RequestBody @Valid EnrollmentPlanAddForm addForm) {
        return enrollmentPlanService.add(addForm, SmartRequestUtil.getRequestUser());
    }

    @Operation(summary = "更新招生计划 <AUTHOR>
    @PostMapping("/update")
    public ResponseDTO<String> update(@RequestBody @Valid EnrollmentPlanUpdateForm updateForm) {
        return enrollmentPlanService.update(updateForm, SmartRequestUtil.getRequestUser());
    }

    @Operation(summary = "删除招生计划 <AUTHOR>
    @PostMapping("/delete/{enrollmentPlanId}")
    public ResponseDTO<String> delete(@PathVariable Long enrollmentPlanId) {
        return enrollmentPlanService.delete(enrollmentPlanId);
    }

    @Operation(summary = "批量删除招生计划 <AUTHOR>
    @PostMapping("/batchDelete")
    public ResponseDTO<String> batchDelete(@RequestBody List<Long> idList) {
        return enrollmentPlanService.batchDelete(idList);
    }

    @Operation(summary = "查询招生计划详情 <AUTHOR>
    @GetMapping("/detail/{enrollmentPlanId}")
    public ResponseDTO<EnrollmentPlanVO> getDetail(@PathVariable Long enrollmentPlanId) {
        return enrollmentPlanService.getDetail(enrollmentPlanId);
    }

    @Operation(summary = "查询所有年度列表 <AUTHOR>
    @GetMapping("/queryYearList")
    public ResponseDTO<List<Integer>> queryYearList() {
        return enrollmentPlanService.queryYearList();
    }

    @Operation(summary = "根据年度统计招生计划数量 <AUTHOR>
    @GetMapping("/countByYear")
    public ResponseDTO<Map<Integer, Integer>> countByYear() {
        return enrollmentPlanService.countByYear();
    }

    @Operation(summary = "年度复制 <AUTHOR>
    @PostMapping("/copyFromPreviousYear")
    public ResponseDTO<String> copyFromPreviousYear(@RequestBody @Valid EnrollmentPlanYearCopyForm copyForm) {
        return enrollmentPlanService.copyFromPreviousYear(copyForm, SmartRequestUtil.getRequestUser());
    }

    @Operation(summary = "导出Excel <AUTHOR>
    @PostMapping("/exportExcel")
    public void exportExcel(@RequestBody EnrollmentPlanQueryForm queryForm, HttpServletResponse response) throws IOException {
        enrollmentPlanService.exportExcel(queryForm, response);
    }

    @Operation(summary = "下载导入模板 <AUTHOR>
    @GetMapping("/downloadTemplate")
    public void downloadTemplate(HttpServletResponse response) throws IOException {
        enrollmentPlanService.downloadTemplate(response);
    }

    @Operation(summary = "导入Excel <AUTHOR>
    @PostMapping("/importExcel")
    public ResponseDTO<String> importExcel(@RequestParam("file") MultipartFile file, 
                                          @RequestParam("year") Integer year) {
        return enrollmentPlanService.importExcel(file, year, SmartRequestUtil.getRequestUser());
    }
}
