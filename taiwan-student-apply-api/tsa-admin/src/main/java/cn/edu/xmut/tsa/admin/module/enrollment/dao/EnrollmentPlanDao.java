package cn.edu.xmut.tsa.admin.module.enrollment.dao;

import cn.edu.xmut.tsa.admin.module.enrollment.domain.entity.EnrollmentPlanEntity;
import cn.edu.xmut.tsa.admin.module.enrollment.domain.form.EnrollmentPlanQueryForm;
import cn.edu.xmut.tsa.admin.module.enrollment.domain.vo.EnrollmentPlanVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 招生计划 DAO
 *
 * <AUTHOR>
 * @Date 2025-06-20
 */
@Mapper
public interface EnrollmentPlanDao extends BaseMapper<EnrollmentPlanEntity> {

    /**
     * 分页查询招生计划列表
     */
    List<EnrollmentPlanVO> queryPage(Page<?> page, @Param("queryForm") EnrollmentPlanQueryForm queryForm);

    /**
     * 查询招生计划列表（用于导出）
     */
    List<EnrollmentPlanVO> queryList(@Param("queryForm") EnrollmentPlanQueryForm queryForm);

    /**
     * 根据年度查询招生计划实体列表
     */
    List<EnrollmentPlanEntity> queryByYearEntity(@Param("year") Integer year);

    /**
     * 查询所有年度列表
     */
    List<Integer> queryYearList();

    /**
     * 根据年度统计招生计划数量
     */
    List<EnrollmentPlanVO> countByYear();

    /**
     * 检查专业是否存在（用于导入时重复性校验）
     */
    Long checkMajorExists(@Param("year") Integer year, @Param("majorName") String majorName, @Param("collegeName") String collegeName);

    /**
     * 批量插入招生计划
     */
    void batchInsert(@Param("list") List<EnrollmentPlanEntity> list);
}
