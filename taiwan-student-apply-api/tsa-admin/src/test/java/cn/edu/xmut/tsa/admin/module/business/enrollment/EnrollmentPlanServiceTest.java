package cn.edu.xmut.tsa.admin.module.business.enrollment;

import cn.edu.xmut.tsa.admin.module.business.enrollment.domain.entity.EnrollmentPlanEntity;
import cn.edu.xmut.tsa.admin.module.business.enrollment.domain.form.EnrollmentPlanAddForm;
import cn.edu.xmut.tsa.admin.module.business.enrollment.domain.form.EnrollmentPlanQueryForm;
import cn.edu.xmut.tsa.admin.module.business.enrollment.domain.form.EnrollmentPlanUpdateForm;
import cn.edu.xmut.tsa.admin.module.business.enrollment.domain.form.EnrollmentPlanYearCopyForm;
import cn.edu.xmut.tsa.admin.module.business.enrollment.service.EnrollmentPlanService;
import cn.edu.xmut.tsa.base.common.domain.PageResult;
import cn.edu.xmut.tsa.base.common.domain.RequestUser;
import cn.edu.xmut.tsa.base.common.domain.ResponseDTO;
import cn.edu.xmut.tsa.base.common.enumeration.UserTypeEnum;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Arrays;

/**
 * 招生计划功能测试
 *
 * <AUTHOR>
 * @Date 2025-06-20 
 * @Copyright 台湾学生招考管理系统
 */
@SpringBootTest
@ActiveProfiles("test")
public class EnrollmentPlanServiceTest {

    @Resource
    private EnrollmentPlanService enrollmentPlanService;

    /**
     * 测试新增招生计划
     */
    @Test
    public void testAdd() {
        // 准备测试数据
        EnrollmentPlanAddForm addForm = new EnrollmentPlanAddForm();
        addForm.setYear(2025);
        addForm.setSubjectType("理工类");
        addForm.setMajorName("计算机科学与技术");
        addForm.setCollegeName("计算机科学学院");
        addForm.setEducationDuration(4);
        addForm.setEnrollmentCount(100);
        addForm.setRemark("测试专业");
        addForm.setSort(1);

        RequestUser requestUser = createTestUser();

        // 执行测试
        ResponseDTO<String> result = enrollmentPlanService.add(addForm, requestUser);
        
        // 验证结果
        System.out.println("新增结果: " + result.getOk() + ", 消息: " + result.getMsg());
    }

    /**
     * 测试分页查询
     */
    @Test
    public void testQueryPage() {
        // 准备查询条件
        EnrollmentPlanQueryForm queryForm = new EnrollmentPlanQueryForm();
        queryForm.setYear(2025);
        queryForm.setPageNum(1);
        queryForm.setPageSize(10);

        // 执行查询
        PageResult result = enrollmentPlanService.queryPage(queryForm);
        
        // 输出结果
        System.out.println("查询结果总数: " + result.getTotal());
        System.out.println("当前页数据: " + result.getList().size());
    }

    /**
     * 测试更新
     */
    @Test
    public void testUpdate() {
        // 这里需要先有一条数据，实际测试时需要替换为真实的ID
        EnrollmentPlanUpdateForm updateForm = new EnrollmentPlanUpdateForm();
        updateForm.setEnrollmentPlanId(1L); // 假设ID为1
        updateForm.setYear(2025);
        updateForm.setSubjectType("理工类");
        updateForm.setMajorName("软件工程");
        updateForm.setCollegeName("计算机科学学院");
        updateForm.setEducationDuration(4);
        updateForm.setEnrollmentCount(120);
        updateForm.setRemark("更新测试");
        updateForm.setSort(1);

        RequestUser requestUser = createTestUser();

        // 执行更新
        ResponseDTO<String> result = enrollmentPlanService.update(updateForm, requestUser);
        
        // 验证结果
        System.out.println("更新结果: " + result.getOk() + ", 消息: " + result.getMsg());
    }

    /**
     * 测试年度复制
     */
    @Test
    public void testCopyFromPreviousYear() {
        // 准备复制表单
        EnrollmentPlanYearCopyForm copyForm = new EnrollmentPlanYearCopyForm();
        copyForm.setFromYear(2024);
        copyForm.setToYear(2025);

        RequestUser requestUser = createTestUser();

        // 执行复制
        ResponseDTO<String> result = enrollmentPlanService.copyFromPreviousYear(copyForm, requestUser);
        
        // 验证结果
        System.out.println("复制结果: " + result.getOk() + ", 消息: " + result.getMsg());
    }

    /**
     * 测试批量删除
     */
    @Test
    public void testBatchDelete() {
        // 准备要删除的ID列表（这里使用假的ID，实际测试需要真实ID）
        ResponseDTO<String> result = enrollmentPlanService.batchDelete(Arrays.asList(1L, 2L));
        
        // 验证结果
        System.out.println("批量删除结果: " + result.getOk() + ", 消息: " + result.getMsg());
    }

    /**
     * 创建测试用户
     */
    private RequestUser createTestUser() {
        RequestUser user = new RequestUser();
        user.setUserId(1L);
        user.setUserName("测试用户");
        user.setUserType(UserTypeEnum.ADMIN_EMPLOYEE);
        return user;
    }
}
