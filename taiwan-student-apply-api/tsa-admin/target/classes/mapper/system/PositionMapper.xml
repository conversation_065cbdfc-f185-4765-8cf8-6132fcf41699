<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.edu.xmut.tsa.admin.module.system.position.dao.PositionDao">

    <!-- 分页查询 -->
    <select id="queryPage" resultType="cn.edu.xmut.tsa.admin.module.system.position.domain.vo.PositionVO">
        SELECT
        *
        FROM t_position
        <where>
            deleted_flag = #{queryForm.deletedFlag}
            <!--关键字查询-->
            <if test="queryForm.keywords != null and queryForm.keywords != ''">
                AND INSTR(t_position.position_name,#{queryForm.keywords})
            </if>
        </where>
    </select>


    <select id="queryList" resultType="cn.edu.xmut.tsa.admin.module.system.position.domain.vo.PositionVO">
        SELECT *
        FROM t_position
        where deleted_flag = #{deletedFlag}
    </select>
</mapper>