<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.edu.xmut.tsa.admin.module.business.enrollment.dao.EnrollmentPlanDao">

    <!-- 分页查询招生计划列表 -->
    <select id="queryPage" resultType="cn.edu.xmut.tsa.admin.module.business.enrollment.domain.vo.EnrollmentPlanVO">
        SELECT
            enrollment_plan_id,
            year,
            subject_type,
            major_name,
            college_name,
            education_duration,
            enrollment_count,
            remark,
            sort,
            create_user_id,
            create_user_name,
            create_time,
            update_time
        FROM t_enrollment_plan
        <where>
            deleted_flag = 0
            <!-- 年度查询 -->
            <if test="queryForm.year != null">
                AND year = #{queryForm.year}
            </if>
            <!-- 科类查询 -->
            <if test="queryForm.subjectType != null and queryForm.subjectType != ''">
                AND subject_type = #{queryForm.subjectType}
            </if>
            <!-- 专业名称查询 -->
            <if test="queryForm.majorName != null and queryForm.majorName != ''">
                AND INSTR(major_name, #{queryForm.majorName})
            </if>
            <!-- 所属学院查询 -->
            <if test="queryForm.collegeName != null and queryForm.collegeName != ''">
                AND INSTR(college_name, #{queryForm.collegeName})
            </if>
            <!-- 关键字查询 -->
            <if test="queryForm.keywords != null and queryForm.keywords != ''">
                AND (
                    INSTR(major_name, #{queryForm.keywords})
                    OR INSTR(college_name, #{queryForm.keywords})
                    OR INSTR(subject_type, #{queryForm.keywords})
                    OR INSTR(remark, #{queryForm.keywords})
                )
            </if>
        </where>
        ORDER BY year DESC, sort ASC, create_time DESC
    </select>

    <!-- 根据年度查询招生计划列表 -->
    <select id="queryByYear" resultType="cn.edu.xmut.tsa.admin.module.business.enrollment.domain.vo.EnrollmentPlanVO">
        SELECT
            enrollment_plan_id,
            year,
            subject_type,
            major_name,
            college_name,
            education_duration,
            enrollment_count,
            remark,
            sort,
            create_user_id,
            create_user_name,
            create_time,
            update_time
        FROM t_enrollment_plan
        WHERE year = #{year} AND deleted_flag = 0
        ORDER BY sort ASC, create_time DESC
    </select>

    <!-- 根据年度查询招生计划实体列表 -->
    <select id="queryByYearEntity" resultType="cn.edu.xmut.tsa.admin.module.business.enrollment.domain.entity.EnrollmentPlanEntity">
        SELECT
            enrollment_plan_id,
            year,
            subject_type,
            major_name,
            college_name,
            education_duration,
            enrollment_count,
            remark,
            sort,
            create_user_id,
            create_user_name,
            create_time,
            update_time
        FROM t_enrollment_plan
        WHERE year = #{year} AND deleted_flag = 0
        ORDER BY sort ASC, create_time DESC
    </select>

    <!-- 查询招生计划列表（用于导出） -->
    <select id="queryList" resultType="cn.edu.xmut.tsa.admin.module.business.enrollment.domain.vo.EnrollmentPlanVO">
        SELECT
            enrollment_plan_id,
            year,
            subject_type,
            major_name,
            college_name,
            education_duration,
            enrollment_count,
            remark,
            sort,
            create_user_id,
            create_user_name,
            create_time,
            update_time
        FROM t_enrollment_plan
        <where>
            deleted_flag = 0
            <!-- 年度查询 -->
            <if test="queryForm.year != null">
                AND year = #{queryForm.year}
            </if>
            <!-- 科类查询 -->
            <if test="queryForm.subjectType != null and queryForm.subjectType != ''">
                AND subject_type = #{queryForm.subjectType}
            </if>
            <!-- 专业名称查询 -->
            <if test="queryForm.majorName != null and queryForm.majorName != ''">
                AND INSTR(major_name, #{queryForm.majorName})
            </if>
            <!-- 所属学院查询 -->
            <if test="queryForm.collegeName != null and queryForm.collegeName != ''">
                AND INSTR(college_name, #{queryForm.collegeName})
            </if>
            <!-- 关键字查询 -->
            <if test="queryForm.keywords != null and queryForm.keywords != ''">
                AND (
                    INSTR(major_name, #{queryForm.keywords})
                    OR INSTR(college_name, #{queryForm.keywords})
                    OR INSTR(subject_type, #{queryForm.keywords})
                    OR INSTR(remark, #{queryForm.keywords})
                )
            </if>
        </where>
        ORDER BY year DESC, sort ASC, create_time DESC
    </select>

    <!-- 获取招生计划详情 -->
    <select id="getDetail" resultType="cn.edu.xmut.tsa.admin.module.business.enrollment.domain.vo.EnrollmentPlanVO">
        SELECT
            enrollment_plan_id,
            year,
            subject_type,
            major_name,
            college_name,
            education_duration,
            enrollment_count,
            remark,
            sort,
            create_user_id,
            create_user_name,
            create_time,
            update_time
        FROM t_enrollment_plan
        WHERE enrollment_plan_id = #{enrollmentPlanId} AND deleted_flag = 0
    </select>

    <!-- 批量插入招生计划 -->
    <insert id="batchInsert" parameterType="list">
        INSERT INTO t_enrollment_plan (
            year,
            subject_type,
            major_name,
            college_name,
            education_duration,
            enrollment_count,
            remark,
            sort,
            create_user_id,
            create_user_name,
            create_time,
            update_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.year},
                #{item.subjectType},
                #{item.majorName},
                #{item.collegeName},
                #{item.educationDuration},
                #{item.enrollmentCount},
                #{item.remark},
                #{item.sort},
                #{item.createUserId},
                #{item.createUserName},
                #{item.createTime},
                #{item.updateTime}
            )
        </foreach>
    </insert>

    <!-- 根据年度、科类和专业名称检查重复 -->
    <select id="checkDuplicate" resultType="int">
        SELECT COUNT(1)
        FROM t_enrollment_plan
        WHERE year = #{year}
          AND major_name = #{majorName}
          AND subject_type = #{subjectType}
          AND deleted_flag = 0
        <if test="enrollmentPlanId != null">
            AND enrollment_plan_id != #{enrollmentPlanId}
        </if>
    </select>

    <!-- 根据年度统计招生计划数量 -->
    <select id="countByYear" resultType="int">
        SELECT COUNT(1)
        FROM t_enrollment_plan
        WHERE year = #{year} AND deleted_flag = 0
    </select>

    <!-- 根据年度删除招生计划（逻辑删除） -->
    <update id="deleteByYear">
        UPDATE t_enrollment_plan
        SET deleted_flag = 1, update_time = NOW()
        WHERE year = #{year} AND deleted_flag = 0
    </update>

</mapper>
