---
applyTo: '**.vue'
---
# Vue3 开发规范

## 命名规范

### 项目命名

全部采用小写方式， 以中划线分隔。

```bash
# 正例
smart-admin
vue-project

# 反例
mall_management-system
mallManagementSystem
```

### 目录、文件命名

目录、文件名 均以 小写方式， 以中划线分隔。

```bash
# 正例
/head-search/
/shopping-car/
smart-logo.png
role-form.vue

# 反例
/headSearch/
smartLogo.png
RoleForm.vue
```

## 代码风格

### 引号和分号

- HTML 标签属性使用双引号
- JavaScript 字符串使用单引号
- JavaScript 语句必须以分号结尾

```html
<!-- HTML 中使用双引号 -->
<div class="container" id="main"></div>
```

```javascript
// JavaScript 中使用单引号和分号
const message = 'Hello World';
const data = { name: '<PERSON>' };
```

## Vue3 组合式 API规范

### 使用setup语法糖

- 组件必须使用 `setup` 语法糖
- `setup` 大法方便简洁
- 全局都要使用 `setup` 语法糖

```vue
<script setup>
// 必须使用 setup 语法糖
</script>
```

### Vue3 组合式 Composition API 规范

组件内必须使用模块化思想，把代码进行拆分，将相关的变量和代码写到一起，并使用行注释进行分块

```vue
<script setup>
  import { ref, reactive } from 'vue';
  import { message } from 'ant-design-vue';
  import { SmartLoading } from '/@/components/framework/smart-loading';
  import _ from 'lodash';
  import { categoryApi } from '/@/api/business/category/category-api';
  import { smartSentry } from '/@/lib/smart-sentry';

  // emit
  const emit = defineEmits('reloadList');

  // 组件
  const formRef = ref();

  // ------------------------------ 显示 、隐藏操作的 变量和方法------------------------------

  // 是否展示抽屉
  const visible = ref(false);
  // 显示
  function showModal(categoryType, parentId, rowData) {
    Object.assign(form, formDefault);
    form.categoryType = categoryType;
    form.parentId = parentId;
    if (rowData && !_.isEmpty(rowData)) {
      Object.assign(form, rowData);
    }
    visible.value = true;
  }
  // 隐藏
  function onClose() {
    Object.assign(form, formDefault);
    visible.value = false;
  }

  // ------------------------------ 表单的  变量和方法 ------------------------------
  // 查询表单默认值
  const formDefault = {
    categoryId: undefined, //分类id
    categoryName: '', //分类名字
    categoryType: 1, // 分类类型
    parentId: undefined, // 父级id
    disabledFlag: false, //是否禁用
  };
  // 查询表单
  let form = reactive({ ...formDefault });
  // 表单校验规则
  const rules = {
    categoryName: [{ required: true, message: '请输入分类名称' }],
  };

  function onSubmit() {
    formRef.value
      .validate()
      .then(async () => {
        SmartLoading.show();
        try {
          if (form.categoryId) {
            await categoryApi.updateCategory(form);
          } else {
            await categoryApi.addCategory(form);
          }
          message.success(`${form.categoryId ? '修改' : '添加'}成功`);
          emit('reloadList', form.parentId);
          onClose();
        } catch (error) {
          smartSentry.captureError(error);
        } finally {
          SmartLoading.hide();
        }
      })
      .catch((error) => {
        console.log('error', error);
        message.error('参数验证错误，请仔细填写表单数据!');
      });
  }

  defineExpose({
    showModal,
  });
</script>
```

### 模板引用变量Ref

- 使用 `ref` 方法，参数为空进行声明变量
- 变量必须以 `Ref` 为结尾
- `template` 中的 `ref` 也必须以 `Ref` 为结尾

### 变量和方法的注释

- 变量必须都加上注释
- 方法必须加上 jsdoc 注释

## Vue3 组件规范

### 组件文件命名

- 组件文件名应该为 pascal-case 格式
- 和父组件紧密耦合的子组件应该以父组件名作为前缀命名

```bash
# 正例
components/
├── todo-list.vue
├── todo-list-item.vue
├── todo-list-item-button.vue
├── user-profile-options.vue （完整单词）

# 反例
components/
├── MyComponent.vue
├── todoList.vue
├── UProfOpts.vue （使用了缩写）
```

### 组件属性

组件属性较多，应该主动换行。

```vue
<!-- 正例：属性较多时主动换行 -->
<MyComponent foo="a" bar="b" baz="c"
    foo="a" bar="b" baz="c"
    foo="a" bar="b" baz="c"
 ></MyComponent>

<!-- 反例：所有属性在一行 -->
<MyComponent foo="a" bar="b" baz="c" :data="tableData" @click="handleClick"/>
```

### 模板中表达式

组件模板应该只包含简单的表达式，复杂的表达式则应该重构为计算属性或方法。复杂表达式会让你的模板变得不那么声明式。应该尽量描述应该出现的是什么，而非如何计算那个值。而且计算属性和方法使得代码可以重用。

### 标签顺序

单文件组件应该总是让标签顺序保持为 `<template>` 、`<script>`、 `<style>`

```vue
<!-- 正例 -->
<template>
  <!-- 模板内容 -->
</template>

<script setup>
// 脚本内容
</script>

<style scoped>
/* 样式内容 */
</style>
```

## Vue Router 规范

### path 和 name 命名规范

- `pathkebab-case` 命名规范
- `path` 必须以 `/` 开头，即使是 `children` 里的 `path` 也要以 `/` 开头
- `name` 命名规范采用 `KebabCase` 命名规范且和` component` 组件名保持一致！

```javascript
const routes = [
  {
    path: '/user-center', // kebab-case，以 / 开头
    name: 'UserCenter',   // PascalCase，与组件名一致
    component: () => import('@/views/user/user-center.vue')
  },
  {
    path: '/order',
    name: 'OrderManagement',
    children: [
      {
        path: '/order/order-list', // 子路由也以 / 开头
        name: 'OrderList',
        component: () => import('@/views/order/order-list.vue')
      }
    ]
  }
];
```

### 页面传参

使用 路由参数进行传参，即 `{query:param}`

```javascript
// 正例：使用 query 传参
const userId = '123';
this.$router.push({ 
  name: 'UserCenter', 
  query: { id: userId } 
});
```

### Vue 目录结构规范

```bash
src/                               源码目录
├── api/                              所有api接口
├── assets/                           静态资源，images, icons, styles等
├── components/                       公用组件
├── config/                           配置信息
├── constants/                        常量信息，项目所有Enum, 全局常量等
├── directives/                       自定义指令
├── i18n/                             国际化
├── lib/                              外部引用的插件存放及修改文件
├── mock/                             模拟接口，临时存放
├── plugins/                          插件，全局使用
├── router/                           路由，统一管理
├── store/                            vuex, 统一管理
├── theme/                            自定义样式主题
├── utils/                            工具类
├── views/                            视图目录
├── ├── role/                             role模块名
├── ├── ├── role-list.vue                    role列表页面
├── ├── ├── role-add.vue                     role新建页面
├── ├── ├── role-update.vue                  role更新页面
├── ├── ├── index.less                      role模块样式
├── ├── ├── components                      role模块通用组件文件夹
├── ├── employee/                         employee模块
```

#### api 目录结构规范

- api文件要以api为结尾，比如 `employee-api.js`、`login-api.js`，方便查找
- api文件必须导出对象必须以`Api`为结尾，如：`employeeApi`、`noticeApi`
- api中以一个对象将方法包裹
- api中的注释，必须和后端 swagger 文档保持一致，同时保留后端作者

```javascript
// user-api.js
import { request } from '@/utils/request';

export const userApi = {
  /**
   * @description: 查询用户列表 <AUTHOR>
   * @param {Object} params 查询参数
   * @return {Promise} 用户列表
   */
  getUserList: (params) => {
    return request.get('/user/list', { params });
  },

  /**
   * @description: 创建用户 <AUTHOR>
   * @param {Object} userData 用户数据
   * @return {Promise} 创建结果
   */
  createUser: (userData) => {
    return request.post('/user/create', userData);
  }
};
```

#### assets 目录

assets 为静态资源，里面存放 images, styles, icons 等静态资源，静态资源命名格式为 `kebab-case`

#### components 目录

此目录应按照组件进行目录划分，目录命名为 `kebab-case`，一个组件必须一个单独的目录

#### constants 目录

- 常量文件要以 `const` 为结尾，比如 `login-const.js`、`file-const.js`
- 变量要：大写下划线，比如 `LOGIN_RESULT_ENUM`、`LOGIN_SUCCESS`、`LOGIN_FAIL`
- 如果是 枚举，变量必须以 `ENUM` 为结尾，如：`LOGIN_RESULT_ENUM`、`CODE_FRONT_COMPONENT_ENUM`

#### router 与 store 目录

- `router` 尽量按照 views 中的结构保持一致
- `store` 按照业务进行拆分不同的 js 文件

#### views 目录

- 如果是列表页面，要以list为结尾，如 `role-list.vue`、`cache-list.vue`
- 如果是 表单页面，要以 form为结尾，如 `role-form.vue、`notice-add-form.vue`
- 如果是 modal弹窗，要以 `modal` 为结尾，如 表单弹窗 `role-form-modal.vue`，详情 `role-detail-modal.vue`
- 如果是 drawer 抽屉页面，要同上以 `Drawer` 为结尾

## 其他

- 尽量使用 `components` 目录下已存在的通用组件