# 招生计划管理功能部署指南

## 环境要求

### 后端环境

- JDK 8+
- Maven 3.6+
- MySQL 8.0+
- Redis 6.0+

### 前端环境

- Node.js 16+
- pnpm 或 npm

## 部署步骤

### 1. 数据库初始化

1. 执行主表结构创建脚本：

```sql
-- 确保已执行 taiwan_student_apply.sql 中的 t_enrollment_plan 表创建语句
```

2. 插入测试数据（可选）：

```bash
mysql -u root -p taiwan_student_apply < sql/test_data_enrollment_plan.sql
```

### 2. 后端部署

1. 进入后端项目目录：

```bash
cd taiwan-student-apply-api
```

2. 编译项目：

```bash
mvn clean compile
```

3. 运行单元测试：

```bash
mvn test -Dtest=cn.edu.xmut.tsa.admin.module.business.enrollment.EnrollmentPlanServiceTest
```

4. 启动应用：

```bash
cd tsa-admin
mvn spring-boot:run
```

### 3. 前端部署

1. 进入前端项目目录：

```bash
cd taiwan-student-apply-web
```

2. 安装依赖：

```bash
pnpm install
# 或使用 npm install
```

3. 启动开发服务器：

```bash
pnpm dev
# 或使用 npm run dev
```

4. 构建生产版本：

```bash
pnpm build
# 或使用 npm run build
```

## 功能验证

### 1. 后端 API 验证

启动后端服务后，可以通过以下方式验证 API：

1. **Swagger 文档**：访问 `http://localhost:8080/swagger-ui.html`
2. **API 测试**：
   - GET `/enrollmentPlan/queryPage` - 分页查询
   - POST `/enrollmentPlan/add` - 新增招生计划
   - PUT `/enrollmentPlan/update` - 更新招生计划
   - DELETE `/enrollmentPlan/batchDelete` - 批量删除

### 2. 前端功能验证

启动前端服务后，可以验证以下功能：

1. **页面访问**：访问 `http://localhost:3000/business/enrollment-plan`
2. **基础功能**：
   - 查询列表
   - 新增/编辑
   - 删除操作
   - 详情查看
3. **高级功能**：
   - 年度复制
   - 导入导出
   - 批量操作

## 权限配置

需要在系统权限管理中配置以下权限：

```sql
-- 示例权限配置SQL（根据实际权限表结构调整）
INSERT INTO sys_permission (permission_code, permission_name, parent_id, sort) VALUES
('business:enrollment-plan', '招生计划管理', NULL, 1),
('business:enrollment-plan:query', '查询', (SELECT id FROM sys_permission WHERE permission_code = 'business:enrollment-plan'), 1),
('business:enrollment-plan:add', '新增', (SELECT id FROM sys_permission WHERE permission_code = 'business:enrollment-plan'), 2),
('business:enrollment-plan:update', '更新', (SELECT id FROM sys_permission WHERE permission_code = 'business:enrollment-plan'), 3),
('business:enrollment-plan:delete', '删除', (SELECT id FROM sys_permission WHERE permission_code = 'business:enrollment-plan'), 4),
('business:enrollment-plan:detail', '详情', (SELECT id FROM sys_permission WHERE permission_code = 'business:enrollment-plan'), 5),
('business:enrollment-plan:export', '导出', (SELECT id FROM sys_permission WHERE permission_code = 'business:enrollment-plan'), 6),
('business:enrollment-plan:import', '导入', (SELECT id FROM sys_permission WHERE permission_code = 'business:enrollment-plan'), 7),
('business:enrollment-plan:year-copy', '年度复制', (SELECT id FROM sys_permission WHERE permission_code = 'business:enrollment-plan'), 8);
```

## 菜单配置

需要在系统菜单管理中添加招生计划管理菜单：

```sql
-- 示例菜单配置SQL（根据实际菜单表结构调整）
INSERT INTO sys_menu (menu_name, menu_type, parent_id, path, component, permission, sort) VALUES
('招生计划管理', 1, (SELECT id FROM sys_menu WHERE menu_name = '业务管理'), '/business/enrollment-plan', 'business/enrollment-plan/enrollment-plan-list', 'business:enrollment-plan:query', 1);
```

## 常见问题

### 1. 编译错误

**问题**：Maven 编译失败
**解决**：

- 检查 JDK 版本是否正确
- 确保网络连接正常，能够下载依赖
- 清理并重新编译：`mvn clean compile`

### 2. 数据库连接错误

**问题**：应用启动时数据库连接失败
**解决**：

- 检查数据库连接配置
- 确保数据库服务正在运行
- 验证用户名密码正确

### 3. 前端页面无法访问

**问题**：前端路由 404 错误
**解决**：

- 检查路由配置是否正确引入
- 确保组件文件路径正确
- 检查权限配置

### 4. 导入导出功能异常

**问题**：Excel 导入导出失败
**解决**：

- 检查 POI 依赖是否正确
- 确保文件格式正确
- 检查文件大小限制

## 联系方式

如遇到问题，请联系开发团队：

- 邮箱：<EMAIL>
- 微信：zhuda1024
