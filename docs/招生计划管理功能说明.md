# 招生计划管理功能

## 功能概述

台湾学生招考管理系统的"招生计划管理"功能模块，用于管理各年度的招生计划信息。

## 主要功能

### 1. 招生计划管理

- **查询功能**：支持按年度、科类、专业名称、所属学院等条件查询
- **新增功能**：添加新的招生计划
- **编辑功能**：修改现有招生计划信息
- **删除功能**：单条/批量删除招生计划
- **详情查看**：查看招生计划的详细信息

### 2. 年度复制

- 支持将某年度的招生计划复制到新年度
- 可选择复制的字段（科类、专业名称、学院、学制等）
- 避免重复输入，提高工作效率

### 3. 导入导出

- **导出功能**：将招生计划数据导出为 Excel 文件
- **导入功能**：通过 Excel 文件批量导入招生计划
- **模板下载**：提供标准的导入模板
- **错误提示**：导入时提供详细的错误信息

## 数据字段

| 字段名称 | 类型    | 必填 | 说明                |
| -------- | ------- | ---- | ------------------- |
| 招生年度 | Integer | 是   | 招生计划所属年度    |
| 科类     | String  | 是   | 文科/理科/艺术/体育 |
| 专业名称 | String  | 是   | 招生专业名称        |
| 所属学院 | String  | 是   | 专业所属学院        |
| 学制     | Integer | 是   | 学制年限(1-8 年)    |
| 计划人数 | Integer | 是   | 招生计划人数        |
| 排序     | Integer | 是   | 显示排序            |
| 备注     | String  | 否   | 备注信息            |

## 技术架构

### 后端架构

```
Controller -> Service -> Manager -> DAO -> MySQL
```

### 前端架构

```
Vue3 + Ant Design Vue + Vite
```

## 文件结构

### 后端文件

```
tsa-admin/src/main/java/cn/edu/xmut/tsa/admin/module/business/enrollment/
├── constant/
│   └── EnrollmentPlanConst.java           # 常量定义
├── controller/
│   └── EnrollmentPlanController.java      # REST接口
├── dao/
│   └── EnrollmentPlanDao.java             # 数据访问接口
├── domain/
│   ├── entity/
│   │   └── EnrollmentPlanEntity.java      # 实体类
│   ├── form/
│   │   ├── EnrollmentPlanAddForm.java     # 新增表单
│   │   ├── EnrollmentPlanUpdateForm.java  # 更新表单
│   │   ├── EnrollmentPlanQueryForm.java   # 查询表单
│   │   ├── EnrollmentPlanYearCopyForm.java # 年度复制表单
│   │   └── EnrollmentPlanImportForm.java  # 导入表单
│   └── vo/
│       ├── EnrollmentPlanVO.java          # 视图对象
│       └── EnrollmentPlanExportVO.java    # 导出对象
├── manager/
│   └── EnrollmentPlanManager.java         # 管理器
└── service/
    └── EnrollmentPlanService.java         # 业务服务

tsa-admin/src/main/resources/mapper/business/enrollment/
└── EnrollmentPlanMapper.xml               # MyBatis映射文件

tsa-admin/src/test/java/cn/edu/xmut/tsa/admin/module/business/enrollment/
└── EnrollmentPlanServiceTest.java         # 单元测试
```

### 前端文件

```
taiwan-student-apply-web/src/
├── api/business/
│   └── enrollment-plan-api.js             # API接口
├── constants/business/
│   └── enrollment-plan-const.js           # 前端常量
├── router/business/
│   └── enrollment-plan.js                 # 路由配置
└── views/business/enrollment-plan/
    ├── enrollment-plan-list.vue           # 主页面
    └── components/
        ├── enrollment-plan-form.vue       # 表单组件
        ├── enrollment-plan-detail.vue     # 详情组件
        ├── enrollment-plan-year-copy.vue  # 年度复制组件
        └── enrollment-plan-import.vue     # 导入组件
```

## 权限配置

需要在系统中配置以下权限：

- `business:enrollment-plan:query` - 查询权限
- `business:enrollment-plan:add` - 新增权限
- `business:enrollment-plan:update` - 更新权限
- `business:enrollment-plan:delete` - 删除权限
- `business:enrollment-plan:detail` - 详情权限
- `business:enrollment-plan:export` - 导出权限
- `business:enrollment-plan:import` - 导入权限
- `business:enrollment-plan:year-copy` - 年度复制权限

## 数据库表

### t_enrollment_plan（招生计划表）

```sql
CREATE TABLE `t_enrollment_plan` (
  `enrollment_plan_id` bigint NOT NULL AUTO_INCREMENT COMMENT '招生计划ID',
  `enrollment_year` int NOT NULL COMMENT '招生年度',
  `category` varchar(20) NOT NULL COMMENT '科类',
  `major_name` varchar(100) NOT NULL COMMENT '专业名称',
  `college_name` varchar(100) NOT NULL COMMENT '所属学院',
  `duration` int NOT NULL COMMENT '学制（年）',
  `plan_count` int NOT NULL COMMENT '计划招生人数',
  `sort` int NOT NULL DEFAULT '1' COMMENT '排序',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `deleted_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标志',
  `create_user_id` bigint NOT NULL COMMENT '创建人ID',
  `create_user_name` varchar(50) NOT NULL COMMENT '创建人姓名',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_user_id` bigint DEFAULT NULL COMMENT '更新人ID',
  `update_user_name` varchar(50) DEFAULT NULL COMMENT '更新人姓名',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`enrollment_plan_id`),
  UNIQUE KEY `uk_year_major` (`enrollment_year`,`major_name`,`deleted_flag`)
) ENGINE=InnoDB COMMENT='招生计划表';
```

## 使用说明

1. **基础管理**：在菜单中找到"招生计划管理"，进入主页面
2. **查询数据**：使用查询条件筛选招生计划
3. **新增计划**：点击"新建"按钮，填写招生计划信息
4. **批量操作**：选择多条数据进行批量删除
5. **年度复制**：使用年度复制功能快速创建新年度计划
6. **导入导出**：使用 Excel 进行批量数据处理

## 注意事项

1. 同一年度内专业名称不能重复
2. 招生人数必须大于 0
3. 学制范围为 1-8 年
4. 导入 Excel 文件大小不超过 5MB
5. 删除操作为逻辑删除，数据仍保留在数据库中

## 开发者信息

- **开发团队**：1024 创新实验室
- **开发时间**：2024 年 1 月
- **技术栈**：Spring Boot + MyBatis + Vue3 + Ant Design Vue
