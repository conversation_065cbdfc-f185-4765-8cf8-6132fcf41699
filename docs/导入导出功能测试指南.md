# 招生计划管理模块导入导出功能测试指南

## 功能概述

招生计划管理模块现已完成导入导出功能的完整实现，支持：

1. 下载导入模板
2. Excel 数据导入（带数据校验）
3. Excel 数据导出
4. 年度计划复制
5. 批量操作

## 后端实现要点

### 1. 导入模板下载 (`GET /enrollmentPlan/downloadTemplate`)

- 生成标准 Excel 模板文件
- 包含示例数据和字段说明
- 支持科类、学制等规范化数据

### 2. 数据导入 (`POST /enrollmentPlan/importExcel`)

**参数：**

- `file`: Excel 文件 (MultipartFile)
- `year`: 导入年度 (Integer)

**校验规则：**

- 文件格式校验：仅支持 .xlsx, .xls
- 基础数据校验：必填字段、数据类型、长度限制
- 业务规则校验：科类枚举值、学制范围(1-8 年)、招生人数范围
- 重复性校验：同年度同专业不能重复
- 内部重复校验：导入文件内部数据不能重复

**错误处理：**

- 详细的行级错误提示
- 所有错误统一返回，便于批量修正
- 事务回滚保证数据一致性

### 3. 数据导出 (`POST /enrollmentPlan/exportExcel`)

- 根据查询条件导出
- 标准 Excel 格式
- 包含完整的计划信息

## 前端实现要点

### 1. 导入功能

- 年度自动选择当前选中年度
- 文件类型和大小校验
- 进度提示和错误反馈
- 支持模板下载

### 2. 导出功能

- 根据当前查询条件导出
- 自动生成文件名（含年度信息）
- Blob 下载处理

## 测试步骤

### 准备工作

1. 执行测试数据 SQL：`test_data_enrollment_plan_enhanced.sql`
2. 启动后端服务和前端开发服务器
3. 登录系统，进入招生计划管理页面

### 测试用例

#### 用例 1：模板下载测试

1. 点击"导入"按钮打开导入对话框
2. 点击"下载导入模板"按钮
3. 验证文件下载成功
4. 打开 Excel 文件验证模板格式和示例数据

#### 用例 2：正常导入测试

1. 基于下载的模板填写测试数据：
   ```
   专业代码 | 专业名称 | 科类 | 学制 | 招生人数 | 学费 | 备注
   TEST01  | 测试专业1 | 理工类 | 4年 | 30 | 5000 | 测试数据
   TEST02  | 测试专业2 | 文科 | 4年 | 25 | 4500 | 测试数据
   ```
2. 选择导入年度（如 2026 年）
3. 上传 Excel 文件
4. 验证导入成功，页面自动刷新显示新数据

#### 用例 3：数据校验测试

创建包含错误的 Excel 文件测试以下情况：

1. **必填字段为空**：专业名称、科类等
2. **科类错误**：填入不支持的科类值
3. **学制错误**：填入"10 年"或"0 年"
4. **招生人数错误**：填入负数或超大数值
5. **重复数据**：与现有数据重复的专业

验证系统能准确识别并提示所有错误。

#### 用例 4：导出功能测试

1. 在列表页面设置筛选条件（如选择特定年度、科类）
2. 点击"导出"按钮
3. 验证 Excel 文件下载成功
4. 打开文件验证数据完整性和格式正确性

#### 用例 5：年度复制测试

1. 点击"年度复制"按钮
2. 选择源年度和目标年度
3. 验证复制成功，目标年度数据正确生成

## 错误处理验证

### 后端错误

- 文件格式错误：上传非 Excel 文件
- 文件过大：上传超过 5MB 文件
- 数据校验错误：各种格式和业务规则错误
- 重复数据：专业重复导入

### 前端错误

- 网络错误处理
- 文件选择错误
- 响应数据解析错误

## 性能考虑

1. **大文件处理**：支持最大 5MB Excel 文件
2. **批量导入**：建议单次导入不超过 1000 条记录
3. **内存优化**：使用 EasyExcel 流式处理
4. **事务控制**：导入失败自动回滚

## 数据格式说明

### 导入 Excel 格式

```
列序号 | 列名 | 数据类型 | 必填 | 说明
0 | 专业代码 | 文本 | 是 | 最长20字符
1 | 专业名称 | 文本 | 是 | 最长100字符
2 | 科类 | 枚举 | 是 | 文科/理科/理工类/艺术/体育
3 | 学制 | 文本 | 是 | 格式：数字+"年"，如"4年"
4 | 招生人数 | 数字 | 是 | 1-9999
5 | 学费(元/年) | 数字 | 是 | 0-99999
6 | 备注 | 文本 | 否 | 最长500字符
```

### 科类枚举值

- 文科
- 理科
- 理工类
- 艺术
- 体育

## 常见问题

### Q1：导入时提示"科类不存在"

**A**：检查 Excel 中科类列的值是否为标准枚举值，注意中文字符和空格。

### Q2：导入成功但数据未显示

**A**：检查导入的年度是否与当前页面选中的年度一致。

### Q3：导出文件打开乱码

**A**：使用 Microsoft Excel 或 WPS 打开，确保编码为 UTF-8。

### Q4：批量导入中途失败

**A**：所有数据都会回滚，修正错误后重新导入即可。

## 后续优化建议

1. **增量导入**：支持"跳过"或"覆盖"重复数据的选择
2. **导入进度**：大文件导入时显示进度条
3. **数据预览**：导入前预览解析结果
4. **导入日志**：记录导入操作的详细日志
5. **模板定制**：支持自定义导入模板格式

---

_测试完成后，如发现问题请及时反馈，以便进一步优化。_
