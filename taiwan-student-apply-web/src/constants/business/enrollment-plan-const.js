/*
 * 招生计划管理常量
 *
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2025-06-20
 */

// 科类枚举
export const SUBJECT_TYPE_ENUM = {
  LIBERAL_ARTS: {
    value: 1,
    desc: '文史科',
  },
  SCIENCE_ENGINEERING: {
    value: 5,
    desc: '理工科',
  },
};

// 科类选项列表
export const SUBJECT_TYPE_OPTIONS = [
  {
    value: SUBJECT_TYPE_ENUM.LIBERAL_ARTS.value,
    label: SUBJECT_TYPE_ENUM.LIBERAL_ARTS.desc,
  },
  {
    value: SUBJECT_TYPE_ENUM.SCIENCE_ENGINEERING.value,
    label: SUBJECT_TYPE_ENUM.SCIENCE_ENGINEERING.desc,
  },
];

// 学制选项列表
export const EDUCATION_DURATION_OPTIONS = [
  { value: 1, label: '1年' },
  { value: 2, label: '2年' },
  { value: 3, label: '3年' },
  { value: 4, label: '4年' },
  { value: 5, label: '5年' },
  { value: 6, label: '6年' },
  { value: 7, label: '7年' },
  { value: 8, label: '8年' },
];

// 表格列配置
export const TABLE_COLUMNS = [
  {
    title: '专业名称',
    dataIndex: 'majorName',
    key: 'majorName',
    width: 200,
    ellipsis: true,
  },
  {
    title: '所属学院',
    dataIndex: 'collegeName',
    key: 'collegeName',
    width: 180,
    ellipsis: true,
  },
  {
    title: '科类',
    dataIndex: 'categoryName',
    key: 'categoryName',
    width: 100,
  },
  {
    title: '学制',
    dataIndex: 'educationDuration',
    key: 'educationDuration',
    width: 80,
    customRender: ({ text }) => `${text}年`,
  },
  {
    title: '招生人数',
    dataIndex: 'enrollmentCount',
    key: 'enrollmentCount',
    width: 100,
  },
  {
    title: '备注',
    dataIndex: 'remark',
    key: 'remark',
    width: 200,
    ellipsis: true,
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 180,
  },
];

// 表单验证规则
export const FORM_RULES = {
  year: [{ required: true, message: '请选择招生年度' }],
  category: [{ required: true, message: '请选择科类' }],
  majorName: [
    { required: true, message: '请输入专业名称' },
    { max: 200, message: '专业名称长度不能超过200个字符' },
  ],
  collegeName: [
    { required: true, message: '请输入所属学院' },
    { max: 200, message: '所属学院长度不能超过200个字符' },
  ],
  educationDuration: [
    { required: true, message: '请选择学制' },
    { type: 'number', min: 1, max: 8, message: '学制必须在1-8年之间' },
  ],
  enrollmentCount: [
    { required: true, message: '请输入招生人数' },
    { type: 'number', min: 0, max: 9999, message: '招生人数必须在0-9999之间' },
  ],
  remark: [{ max: 500, message: '备注长度不能超过500个字符' }],
  sort: [{ type: 'number', min: 0, message: '排序不能小于0' }],
};

// 导入文件限制
export const IMPORT_FILE_CONFIG = {
  accept: '.xlsx,.xls',
  maxSize: 10 * 1024 * 1024, // 10MB
  supportedTypes: ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'application/vnd.ms-excel'],
};
