<template>
  <div class="enrollment-plan-container">
    <!-- 左侧年度列表 -->
    <div class="year-sidebar">
      <div class="year-header">
        <h3>招生年度</h3>
        <a-button type="primary" size="small" @click="showYearCopyModal">
          <template #icon>
            <CopyOutlined />
          </template>
          年度复制
        </a-button>
      </div>
      <div class="year-list">
        <div v-for="year in yearList" :key="year" :class="['year-item', { active: selectedYear === year }]" @click="selectYear(year)">
          <span class="year-text">{{ year }}年</span>
          <span class="year-count">{{ getYearCount(year) }}个专业</span>
        </div>
        <div class="year-item add-year" @click="addNewYear">
          <PlusOutlined />
          <span>新增年度</span>
        </div>
      </div>
    </div>

    <!-- 右侧内容区域 -->
    <div class="content-area">
      <!-- 搜索栏 -->
      <div class="search-container">
        <a-form layout="inline" :model="queryForm">
          <a-form-item label="专业名称">
            <a-input v-model:value="queryForm.majorName" placeholder="请输入专业名称" allow-clear style="width: 200px" />
          </a-form-item>
          <a-form-item label="所属学院">
            <a-input v-model:value="queryForm.collegeName" placeholder="请输入所属学院" allow-clear style="width: 200px" />
          </a-form-item>
          <a-form-item label="科类">
            <a-select v-model:value="queryForm.category" placeholder="请选择科类" allow-clear style="width: 120px">
              <a-select-option v-for="option in SUBJECT_TYPE_OPTIONS" :key="option.value" :value="option.value">
                {{ option.label }}
              </a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item>
            <a-button type="primary" @click="queryPage">
              <template #icon>
                <SearchOutlined />
              </template>
              查询
            </a-button>
            <a-button @click="resetQuery" style="margin-left: 8px">
              <template #icon>
                <ReloadOutlined />
              </template>
              重置
            </a-button>
          </a-form-item>
        </a-form>
      </div>

      <!-- 操作按钮栏 -->
      <div class="action-container">
        <div class="action-left">
          <a-button type="primary" @click="addEnrollmentPlan">
            <template #icon>
              <PlusOutlined />
            </template>
            新增
          </a-button>
          <a-button type="primary" ghost @click="showImportModal" :disabled="!selectedYear">
            <template #icon>
              <ImportOutlined />
            </template>
            导入
          </a-button>
          <a-button type="primary" ghost @click="exportExcel">
            <template #icon>
              <ExportOutlined />
            </template>
            导出
          </a-button>
          <a-button danger @click="batchDelete" :disabled="selectedRowKeys.length === 0">
            <template #icon>
              <DeleteOutlined />
            </template>
            批量删除
          </a-button>
        </div>
        <div class="action-right">
          <span v-if="selectedYear" class="year-info"> 当前年度：{{ selectedYear }}年 </span>
        </div>
      </div>

      <!-- 数据表格 -->
      <div class="table-container">
        <a-table
          :columns="tableColumns"
          :data-source="tableData"
          :loading="tableLoading"
          :pagination="pagination"
          :row-selection="rowSelection"
          row-key="enrollmentPlanId"
          size="middle"
          @change="handleTableChange"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'action'">
              <a-space>
                <a-button type="link" size="small" @click="editEnrollmentPlan(record)"> 编辑 </a-button>
                <a-button type="link" size="small" @click="viewDetail(record)"> 详情 </a-button>
                <a-popconfirm title="确定要删除这条记录吗？" @confirm="deleteEnrollmentPlan(record.enrollmentPlanId)">
                  <a-button type="link" size="small" danger> 删除 </a-button>
                </a-popconfirm>
              </a-space>
            </template>
          </template>
        </a-table>
      </div>
    </div>

    <!-- 新增/编辑对话框 -->
    <EnrollmentPlanForm ref="enrollmentPlanFormRef" @refresh="queryPage" :selectedYear="selectedYear" />

    <!-- 详情对话框 -->
    <EnrollmentPlanDetail ref="enrollmentPlanDetailRef" />

    <!-- 年度复制对话框 -->
    <EnrollmentPlanYearCopy ref="enrollmentPlanYearCopyRef" @refresh="loadYearList" />

    <!-- 导入对话框 -->
    <EnrollmentPlanImport ref="enrollmentPlanImportRef" @refresh="queryPage" :selectedYear="selectedYear" />
  </div>
</template>

<script setup>
  import { reactive, ref, onMounted, computed } from 'vue';
  import { Modal, message } from 'ant-design-vue';
  import { PAGE_SIZE_OPTIONS } from '/@/constants/common-const';
  import { SUBJECT_TYPE_OPTIONS, TABLE_COLUMNS } from '/@/constants/business/enrollment-plan-const';
  import { enrollmentPlanApi } from '/@/api/business/enrollment-plan-api';
  import EnrollmentPlanForm from './components/enrollment-plan-form.vue';
  import EnrollmentPlanDetail from './components/enrollment-plan-detail.vue';
  import EnrollmentPlanYearCopy from './components/enrollment-plan-year-copy.vue';
  import EnrollmentPlanImport from './components/enrollment-plan-import.vue';
  import { SearchOutlined, ReloadOutlined, PlusOutlined, CopyOutlined, ExportOutlined, ImportOutlined, DeleteOutlined } from '@ant-design/icons-vue';

  // ----------------------- 响应式数据 -----------------------

  // 年度相关
  const yearList = ref([]);
  const selectedYear = ref(null);
  const yearCounts = ref({});

  // 查询表单
  const queryForm = reactive({
    year: null,
    category: null,
    majorName: '',
    collegeName: '',
    pageNum: 1,
    pageSize: 10,
  });

  // 表格相关
  const tableData = ref([]);
  const tableLoading = ref(false);
  const total = ref(0);
  const selectedRowKeys = ref([]);

  // 组件引用
  const enrollmentPlanFormRef = ref();
  const enrollmentPlanDetailRef = ref();
  const enrollmentPlanYearCopyRef = ref();
  const enrollmentPlanImportRef = ref();

  // ----------------------- 计算属性 -----------------------

  // 表格列配置（添加操作列）
  const tableColumns = computed(() => [
    ...TABLE_COLUMNS,
    {
      title: '操作',
      key: 'action',
      width: 200,
      fixed: 'right',
    },
  ]);

  // 分页配置
  const pagination = computed(() => ({
    current: queryForm.pageNum,
    pageSize: queryForm.pageSize,
    total: total.value,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
    pageSizeOptions: PAGE_SIZE_OPTIONS,
  }));

  // 行选择配置
  const rowSelection = computed(() => ({
    selectedRowKeys: selectedRowKeys.value,
    onChange: (keys) => {
      selectedRowKeys.value = keys;
    },
  }));

  // ----------------------- 生命周期 -----------------------

  onMounted(() => {
    loadYearList();
  });

  // ----------------------- 年度管理 -----------------------

  async function loadYearList() {
    try {
      const [yearRes, countRes] = await Promise.all([enrollmentPlanApi.queryYearList(), enrollmentPlanApi.countByYear()]);

      yearList.value = yearRes.data || [];
      yearCounts.value = countRes.data || {};

      // 默认选择第一个年度
      if (yearList.value.length > 0 && !selectedYear.value) {
        selectedYear.value = yearList.value[0];
        queryForm.year = selectedYear.value;
        queryPage();
      }
    } catch (error) {
      message.error('加载年度列表失败：' + error.message);
    }
  }

  function selectYear(year) {
    selectedYear.value = year;
    queryForm.year = year;
    queryForm.pageNum = 1;
    selectedRowKeys.value = [];
    queryPage();
  }

  function getYearCount(year) {
    return yearCounts.value[year] || 0;
  }

  function addNewYear() {
    const currentYear = new Date().getFullYear();
    const newYear = currentYear + yearList.value.length;
    selectedYear.value = newYear;
    queryForm.year = newYear;
    addEnrollmentPlan();
  }

  // ----------------------- 查询数据 -----------------------

  async function queryPage() {
    if (!selectedYear.value) return;

    try {
      tableLoading.value = true;
      queryForm.year = selectedYear.value;
      const res = await enrollmentPlanApi.queryPage(queryForm);
      tableData.value = res.data.list || [];
      total.value = res.data.total || 0;
    } catch (error) {
      message.error('查询失败：' + error.message);
    } finally {
      tableLoading.value = false;
    }
  }

  function resetQuery() {
    queryForm.category = null;
    queryForm.majorName = '';
    queryForm.collegeName = '';
    queryForm.pageNum = 1;
    queryPage();
  }

  function handleTableChange(pagination) {
    queryForm.pageNum = pagination.current;
    queryForm.pageSize = pagination.pageSize;
    queryPage();
  }

  // ----------------------- 操作方法 -----------------------

  function addEnrollmentPlan() {
    if (!selectedYear.value) {
      message.warning('请先选择年度');
      return;
    }
    enrollmentPlanFormRef.value.showModal('add', null, selectedYear.value);
  }

  function editEnrollmentPlan(record) {
    enrollmentPlanFormRef.value.showModal('edit', record, selectedYear.value);
  }

  function viewDetail(record) {
    enrollmentPlanDetailRef.value.showModal(record);
  }

  async function deleteEnrollmentPlan(id) {
    try {
      await enrollmentPlanApi.delete(id);
      message.success('删除成功');
      queryPage();
      loadYearList();
    } catch (error) {
      message.error('删除失败：' + error.message);
    }
  }

  function batchDelete() {
    if (selectedRowKeys.value.length === 0) {
      message.warning('请选择要删除的记录');
      return;
    }

    Modal.confirm({
      title: '确认删除',
      content: `确定要删除选中的 ${selectedRowKeys.value.length} 条记录吗？`,
      onOk: async () => {
        try {
          await enrollmentPlanApi.batchDelete(selectedRowKeys.value);
          message.success('批量删除成功');
          selectedRowKeys.value = [];
          queryPage();
          loadYearList();
        } catch (error) {
          message.error('批量删除失败：' + error.message);
        }
      },
    });
  }

  function showYearCopyModal() {
    enrollmentPlanYearCopyRef.value.showModal();
  }

  function showImportModal() {
    if (!selectedYear.value) {
      message.warning('请先选择年度');
      return;
    }
    enrollmentPlanImportRef.value.showModal();
  }

  async function exportExcel() {
    try {
      const params = { ...queryForm };
      const response = await enrollmentPlanApi.exportExcel(params);

      // 创建下载链接
      const blob = new Blob([response], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `招生计划_${selectedYear.value || ''}年_${Date.now()}.xlsx`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      message.success('导出成功');
    } catch (error) {
      message.error('导出失败：' + error.message);
    }
  }
</script>

<style scoped>
  .enrollment-plan-container {
    display: flex;
    height: 100vh;
    background: #f0f2f5;
  }

  .year-sidebar {
    width: 280px;
    background: white;
    border-right: 1px solid #e8e8e8;
    display: flex;
    flex-direction: column;
  }

  .year-header {
    padding: 16px;
    border-bottom: 1px solid #e8e8e8;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .year-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
  }

  .year-list {
    flex: 1;
    overflow-y: auto;
    padding: 8px;
  }

  .year-item {
    padding: 12px 16px;
    margin-bottom: 4px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .year-item:hover {
    background: #f5f5f5;
  }

  .year-item.active {
    background: #e6f7ff;
    border: 1px solid #91d5ff;
  }

  .year-item.add-year {
    border: 1px dashed #d9d9d9;
    color: #666;
    justify-content: center;
    gap: 8px;
  }

  .year-item.add-year:hover {
    border-color: #40a9ff;
    color: #40a9ff;
  }

  .year-text {
    font-weight: 500;
  }

  .year-count {
    font-size: 12px;
    color: #666;
  }

  .content-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 16px;
  }

  .search-container {
    background: white;
    padding: 16px;
    border-radius: 6px;
    margin-bottom: 16px;
  }

  .action-container {
    background: white;
    padding: 16px;
    border-radius: 6px;
    margin-bottom: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .action-left {
    display: flex;
    gap: 8px;
  }

  .year-info {
    color: #666;
    font-size: 14px;
  }

  .table-container {
    background: white;
    border-radius: 6px;
    flex: 1;
    overflow: hidden;
  }
</style>
