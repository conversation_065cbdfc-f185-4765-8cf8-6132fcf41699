<template>
  <div class="enrollment-plan-container">
    <!-- 左侧年度列表 -->
    <div class="year-sidebar">
      <div class="year-header">
        <h3>招生年度</h3>
        <a-button type="primary" size="small" @click="showYearCopyModal" v-privilege="'business:enrollment-plan:year-copy'">
          <template #icon>
            <CopyOutlined />
          </template>
          年度复制
        </a-button>
      </div>
      <div class="year-list">
        <div v-for="year in yearList" :key="year" :class="['year-item', { active: selectedYear === year }]" @click="selectYear(year)">
          <span class="year-text">{{ year }}年</span>
          <span class="year-count">{{ getYearCount(year) }}个专业</span>
        </div>
        <div class="year-item add-year" @click="addNewYear">
          <PlusOutlined />
          <span>新增年度</span>
        </div>
      </div>
    </div>

    <!-- 右侧内容区域 -->
    <div class="main-content">
      <!-- 查询表单 -->
      <a-form class="smart-query-form">
        <a-row class="smart-query-form-row">
          <a-form-item label="科类" class="smart-query-form-item">
            <a-select style="width: 150px" v-model:value="queryForm.category" placeholder="请选择科类" allowClear>
              <a-select-option v-for="item in categoryOptions" :key="item.value" :value="item.value">
                {{ item.label }}
              </a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="专业名称" class="smart-query-form-item">
            <a-input style="width: 200px" v-model:value="queryForm.majorName" placeholder="请输入专业名称" />
          </a-form-item>
          <a-form-item label="所属学院" class="smart-query-form-item">
            <a-select style="width: 150px" v-model:value="queryForm.collegeName" placeholder="请选择学院" allowClear>
              <a-select-option v-for="college in collegeList" :key="college" :value="college">{{ college }}</a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item class="smart-query-form-item smart-margin-left10">
            <a-button-group>
              <a-button v-privilege="'business:enrollment-plan:query'" type="primary" @click="onSearch">
                <template #icon>
                  <SearchOutlined />
                </template>
                查询
              </a-button>
              <a-button v-privilege="'business:enrollment-plan:query'" @click="resetQuery">
                <template #icon>
                  <ReloadOutlined />
                </template>
                重置
              </a-button>
            </a-button-group>
          </a-form-item>
        </a-row>
        <a-row class="smart-query-form-row">
          <a-form-item class="smart-query-form-item">
            <a-button v-privilege="'business:enrollment-plan:add'" type="primary" @click="addEnrollmentPlan">
              <template #icon>
                <PlusOutlined />
              </template>
              新建
            </a-button>
            <a-button v-privilege="'business:enrollment-plan:export'" @click="exportData" class="smart-margin-left10">
              <template #icon>
                <ExportOutlined />
              </template>
              导出
            </a-button>
            <a-button v-privilege="'business:enrollment-plan:import'" @click="showImportModal" class="smart-margin-left10">
              <template #icon>
                <ImportOutlined />
              </template>
              导入
            </a-button>
            <a-button
              v-privilege="'business:enrollment-plan:delete'"
              :disabled="selectedRowKeyList.length === 0"
              @click="batchDelete"
              danger
              class="smart-margin-left10"
            >
              <template #icon>
                <DeleteOutlined />
              </template>
              批量删除
            </a-button>
          </a-form-item>
        </a-row>
      </a-form>

      <!-- 计划表格 -->
      <a-card size="small" :bordered="true">
        <template #title>
          <span>{{ selectedYear }}年度招生计划</span>
          <span class="plan-summary">（共{{ total }}个专业，计划招生{{ totalPlanCount }}人）</span>
        </template>

        <a-table
          size="small"
          bordered
          :loading="tableLoading"
          :dataSource="tableData"
          :columns="columns"
          rowKey="enrollmentPlanId"
          :pagination="false"
          :row-selection="rowSelection"
          :scroll="{ y: 500 }"
        >
          <template #bodyCell="{ record, column }">
            <template v-if="column.dataIndex === 'action'">
              <div class="smart-table-operate">
                <a-button @click="updateEnrollmentPlan(record)" v-privilege="'business:enrollment-plan:update'" type="link">编辑</a-button>
                <a-button @click="viewDetail(record)" v-privilege="'business:enrollment-plan:detail'" type="link">详情</a-button>
                <a-button @click="deleteEnrollmentPlan(record)" v-privilege="'business:enrollment-plan:delete'" type="link" danger>删除</a-button>
              </div>
            </template>
            <template v-else-if="column.dataIndex === 'category'">
              <a-tag :color="getCategoryInfo(record.category).color">
                {{ getCategoryInfo(record.category).label }}
              </a-tag>
            </template>
            <template v-else-if="column.dataIndex === 'planCount'">
              <span style="font-weight: bold; color: #1890ff">{{ record.planCount }}</span>
            </template>
            <template v-else-if="column.dataIndex === 'duration'">
              <span>{{ record.duration }}年</span>
            </template>
          </template>
        </a-table>

        <div class="smart-query-table-page">
          <a-pagination
            showSizeChanger
            showQuickJumper
            show-total
            :pageSizeOptions="PAGE_SIZE_OPTIONS"
            :total="total"
            :current="queryForm.pageNum"
            :pageSize="queryForm.pageSize"
            @change="onTableChange"
            @showSizeChange="onTableChange"
            :showTotal="
              (total) => {
                return `共${total}条`;
              }
            "
          />
        </div>
      </a-card>
    </div>

    <!-- 新增/编辑对话框 -->
    <EnrollmentPlanForm
      ref="enrollmentPlanFormRef"
      @refresh="queryPage"
      :collegeList="collegeList"
      :majorList="majorList"
      :selectedYear="selectedYear"
    />

    <!-- 详情对话框 -->
    <EnrollmentPlanDetail ref="enrollmentPlanDetailRef" />

    <!-- 年度复制对话框 -->
    <EnrollmentPlanYearCopy ref="enrollmentPlanYearCopyRef" @refresh="loadYearList" />

    <!-- 导入对话框 -->
    <EnrollmentPlanImport ref="enrollmentPlanImportRef" @refresh="queryPage" :selectedYear="selectedYear" />
  </div>
</template>

<script setup>
  import { reactive, ref, onMounted, computed } from 'vue';
  import { Modal, message } from 'ant-design-vue';
  import { PAGE_SIZE_OPTIONS } from '/@/constants/common-const';
  import { enrollmentPlanApi } from '/@/api/business/enrollment-plan-api';
  import EnrollmentPlanForm from './components/enrollment-plan-form.vue';
  import EnrollmentPlanDetail from './components/enrollment-plan-detail.vue';
  import EnrollmentPlanYearCopy from './components/enrollment-plan-year-copy.vue';
  import EnrollmentPlanImport from './components/enrollment-plan-import.vue';
  import { SearchOutlined, ReloadOutlined, PlusOutlined, CopyOutlined, ExportOutlined, ImportOutlined, DeleteOutlined } from '@ant-design/icons-vue';

  // ----------------------- 表格列定义 -----------------------
  const columns = reactive([
    {
      title: '科类',
      dataIndex: 'category',
      width: 80,
    },
    {
      title: '专业名称',
      dataIndex: 'majorName',
      width: 200,
    },
    {
      title: '所属学院',
      dataIndex: 'collegeName',
      width: 150,
    },
    {
      title: '学制',
      dataIndex: 'duration',
      width: 80,
    },
    {
      title: '计划人数',
      dataIndex: 'planCount',
      width: 100,
    },
    {
      title: '备注',
      dataIndex: 'remark',
      width: 200,
      ellipsis: true,
    },
    {
      title: '排序',
      dataIndex: 'sort',
      width: 80,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      width: 150,
    },
    {
      title: '操作',
      dataIndex: 'action',
      fixed: 'right',
      width: 180,
    },
  ]);

  // ----------------------- 查询表单 -----------------------
  const queryFormState = {
    enrollmentYear: undefined,
    category: undefined,
    majorName: '',
    collegeName: undefined,
    pageNum: 1,
    pageSize: 10,
  };
  const queryForm = reactive({ ...queryFormState });

  // ----------------------- 年度管理 -----------------------
  const yearList = ref([]);
  const selectedYear = ref(new Date().getFullYear());
  const yearCounts = ref({});

  // ----------------------- 表格数据 -----------------------
  const tableLoading = ref(false);
  const tableData = ref([]);
  const total = ref(0);

  // ----------------------- 选择行 -----------------------
  const selectedRowKeyList = ref([]);
  const rowSelection = {
    onChange: (selectedRowKeys) => {
      selectedRowKeyList.value = selectedRowKeys;
    },
  };

  // ----------------------- 基础数据 -----------------------
  const collegeList = ref([]);
  const majorList = ref([]);

  // ----------------------- 组件引用 -----------------------
  const enrollmentPlanFormRef = ref();
  const enrollmentPlanDetailRef = ref();
  const enrollmentPlanYearCopyRef = ref();
  const enrollmentPlanImportRef = ref();

  // ----------------------- 计算属性 -----------------------
  const totalPlanCount = computed(() => {
    return tableData.value.reduce((sum, item) => sum + (item.planCount || 0), 0);
  });

  // ----------------------- 初始化 -----------------------
  onMounted(() => {
    loadYearList();
    loadBaseData();
  });

  // ----------------------- 年度相关操作 -----------------------
  async function loadYearList() {
    try {
      const res = await enrollmentPlanApi.queryYearList();
      yearList.value = res.data || [];

      // 加载各年度的专业数量
      for (const year of yearList.value) {
        const countRes = await enrollmentPlanApi.queryPage({ enrollmentYear: year, pageSize: 1 });
        yearCounts.value[year] = countRes.data.total || 0;
      }

      // 如果当前选中年度不在列表中，选择第一个年度
      if (yearList.value.length > 0 && !yearList.value.includes(selectedYear.value)) {
        selectedYear.value = yearList.value[0];
      }

      if (selectedYear.value) {
        queryPage();
      }
    } catch (error) {
      console.error('加载年度列表失败：', error);
    }
  }

  function selectYear(year) {
    selectedYear.value = year;
    queryForm.enrollmentYear = year;
    queryForm.pageNum = 1;
    queryPage();
  }

  function getYearCount(year) {
    return yearCounts.value[year] || 0;
  }

  function addNewYear() {
    const currentYear = new Date().getFullYear();
    const newYear = currentYear + yearList.value.length;
    selectedYear.value = newYear;
    queryForm.enrollmentYear = newYear;
    addEnrollmentPlan();
  }

  // ----------------------- 查询数据 -----------------------
  async function queryPage() {
    if (!selectedYear.value) return;

    try {
      tableLoading.value = true;
      queryForm.enrollmentYear = selectedYear.value;
      const res = await enrollmentPlanApi.queryPage(queryForm);
      tableData.value = res.data.list || [];
      total.value = res.data.total || 0;
    } catch (error) {
      message.error('查询失败：' + error.message);
    } finally {
      tableLoading.value = false;
    }
  }

  // ----------------------- 加载基础数据 -----------------------
  async function loadBaseData() {
    try {
      // 加载学院列表
      const collegeRes = await enrollmentPlanApi.queryCollegeList();
      collegeList.value = collegeRes.data || [];

      // 加载专业列表
      const majorRes = await enrollmentPlanApi.queryMajorList();
      majorList.value = majorRes.data || [];
    } catch (error) {
      console.error('加载基础数据失败：', error);
    }
  }

  // ----------------------- 查询操作 -----------------------
  function onSearch() {
    queryForm.pageNum = 1;
    queryPage();
  }

  function resetQuery() {
    Object.assign(queryForm, queryFormState);
    queryForm.enrollmentYear = selectedYear.value;
    queryPage();
  }

  function onTableChange(pageNum, pageSize) {
    queryForm.pageNum = pageNum;
    queryForm.pageSize = pageSize;
    queryPage();
  }

  // ----------------------- 新增操作 -----------------------
  function addEnrollmentPlan() {
    enrollmentPlanFormRef.value.showDrawer(null, selectedYear.value);
  }

  // ----------------------- 编辑操作 -----------------------
  function updateEnrollmentPlan(record) {
    enrollmentPlanFormRef.value.showDrawer(record);
  }

  // ----------------------- 查看详情 -----------------------
  function viewDetail(record) {
    enrollmentPlanDetailRef.value.showModal(record.enrollmentPlanId);
  }

  // ----------------------- 删除操作 -----------------------
  function deleteEnrollmentPlan(record) {
    Modal.confirm({
      title: '提示',
      content: '确定要删除该招生计划吗？',
      onOk: async () => {
        try {
          await enrollmentPlanApi.batchDelete([record.enrollmentPlanId]);
          message.success('删除成功');
          queryPage();
          loadYearList();
        } catch (error) {
          message.error('删除失败：' + error.message);
        }
      },
    });
  }

  // ----------------------- 批量删除 -----------------------
  function batchDelete() {
    if (selectedRowKeyList.value.length === 0) {
      message.warning('请选择要删除的数据');
      return;
    }

    Modal.confirm({
      title: '提示',
      content: `确定要删除选中的${selectedRowKeyList.value.length}条招生计划吗？`,
      onOk: async () => {
        try {
          await enrollmentPlanApi.batchDelete(selectedRowKeyList.value);
          message.success('删除成功');
          selectedRowKeyList.value = [];
          queryPage();
          loadYearList();
        } catch (error) {
          message.error('删除失败：' + error.message);
        }
      },
    });
  }

  // ----------------------- 年度复制 -----------------------
  function showYearCopyModal() {
    enrollmentPlanYearCopyRef.value.showModal();
  }

  // ----------------------- 导出数据 -----------------------
  async function exportData() {
    try {
      const exportParams = { ...queryForm };
      delete exportParams.pageNum;
      delete exportParams.pageSize;

      const res = await enrollmentPlanApi.exportExcel(exportParams);
      // 创建下载链接
      const blob = new Blob([res.data], { type: 'application/vnd.ms-excel' });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${selectedYear.value}年招生计划数据_${new Date().getTime()}.xlsx`;
      link.click();
      window.URL.revokeObjectURL(url);
      message.success('导出成功');
    } catch (error) {
      message.error('导出失败：' + error.message);
    }
  }

  // ----------------------- 导入数据 -----------------------
  function showImportModal() {
    enrollmentPlanImportRef.value.showModal();
  }

  // ----------------------- 工具函数 -----------------------
  function getCategoryInfo(categoryValue) {
    const categories = {
      1: { label: '文科', color: 'blue' },
      5: { label: '理科', color: 'green' },
      6: { label: '理工类', color: 'green' },
      10: { label: '艺术', color: 'purple' },
      15: { label: '体育', color: 'orange' },
    };
    return categories[categoryValue] || { label: '未知', color: 'default' };
  }
</script>

<style lang="less" scoped>
  .enrollment-plan-container {
    display: flex;
    height: calc(100vh - 150px);
    gap: 16px;
  }

  .year-sidebar {
    width: 250px;
    background: #fff;
    border-radius: 6px;
    border: 1px solid #f0f0f0;
    display: flex;
    flex-direction: column;

    .year-header {
      padding: 16px;
      border-bottom: 1px solid #f0f0f0;
      display: flex;
      justify-content: space-between;
      align-items: center;

      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
      }
    }

    .year-list {
      flex: 1;
      overflow-y: auto;
      padding: 8px;

      .year-item {
        padding: 12px 16px;
        margin-bottom: 4px;
        border-radius: 4px;
        cursor: pointer;
        transition: all 0.2s ease;
        display: flex;
        justify-content: space-between;
        align-items: center;

        &:hover {
          background-color: #f5f5f5;
        }

        &.active {
          background-color: #e6f7ff;
          border: 1px solid #91d5ff;
          color: #1890ff;
        }

        &.add-year {
          justify-content: center;
          gap: 8px;
          color: #1890ff;
          border: 1px dashed #d9d9d9;

          &:hover {
            border-color: #1890ff;
            background-color: #f6ffed;
          }
        }

        .year-text {
          font-weight: 500;
        }

        .year-count {
          font-size: 12px;
          color: #999;
          background: #f0f0f0;
          padding: 2px 6px;
          border-radius: 10px;
        }

        &.active .year-count {
          background: #91d5ff;
          color: #1890ff;
        }
      }
    }
  }

  .main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .smart-query-form {
    background-color: #f5f5f5;
    padding: 16px;
    border-radius: 6px;

    .smart-query-form-row {
      display: flex;
      flex-wrap: wrap;
      margin-bottom: -16px;

      .smart-query-form-item {
        margin-bottom: 16px;
        margin-right: 16px;
      }
    }
  }

  .plan-summary {
    font-size: 14px;
    color: #666;
    font-weight: normal;
    margin-left: 8px;
  }

  .smart-table-operate {
    .ant-btn + .ant-btn {
      margin-left: 8px;
    }
  }

  .smart-query-table-page {
    margin-top: 16px;
    text-align: right;
  }

  .smart-margin-left10 {
    margin-left: 10px;
  }

  :deep(.ant-card-head-title) {
    display: flex;
    align-items: center;
  }
</style>
