/*
 * 招生计划管理 API
 *
 * @Author: Ker<PERSON>
 * @Date: 2025-06-20
 */
import { request } from '/@/utils/request';

export const enrollmentPlanApi = {
  /**
   * @description: 分页查询招生计划 <AUTHOR>
   * @param {Object} params 查询参数
   * @return {Promise} 招生计划列表
   */
  queryPage: (params) => {
    return request.post('/enrollmentPlan/queryPage', params);
  },

  /**
   * @description: 新增招生计划 <AUTHOR>
   * @param {Object} data 招生计划数据
   * @return {Promise} 新增结果
   */
  add: (data) => {
    return request.post('/enrollmentPlan/add', data);
  },

  /**
   * @description: 更新招生计划 <AUTHOR>
   * @param {Object} data 招生计划数据
   * @return {Promise} 更新结果
   */
  update: (data) => {
    return request.post('/enrollmentPlan/update', data);
  },

  /**
   * @description: 删除招生计划 <AUTHOR>
   * @param {Number} enrollmentPlanId 招生计划ID
   * @return {Promise} 删除结果
   */
  delete: (enrollmentPlanId) => {
    return request.post(`/enrollmentPlan/delete/${enrollmentPlanId}`);
  },

  /**
   * @description: 批量删除招生计划 <AUTHOR>
   * @param {Array} idList ID列表
   * @return {Promise} 删除结果
   */
  batchDelete: (idList) => {
    return request.post('/enrollmentPlan/batchDelete', idList);
  },

  /**
   * @description: 查询招生计划详情 <AUTHOR>
   * @param {Number} enrollmentPlanId 招生计划ID
   * @return {Promise} 招生计划详情
   */
  getDetail: (enrollmentPlanId) => {
    return request.get(`/enrollmentPlan/detail/${enrollmentPlanId}`);
  },

  /**
   * @description: 查询所有年度列表 <AUTHOR>
   * @return {Promise} 年度列表
   */
  queryYearList: () => {
    return request.get('/enrollmentPlan/queryYearList');
  },

  /**
   * @description: 根据年度统计招生计划数量 <AUTHOR>
   * @return {Promise} 年度统计数据
   */
  countByYear: () => {
    return request.get('/enrollmentPlan/countByYear');
  },

  /**
   * @description: 年度复制 <AUTHOR>
   * @param {Object} data 复制参数
   * @return {Promise} 复制结果
   */
  copyFromPreviousYear: (data) => {
    return request.post('/enrollmentPlan/copyFromPreviousYear', data);
  },

  /**
   * @description: 导出Excel <AUTHOR>
   * @param {Object} params 查询参数
   * @return {Promise} 导出结果
   */
  exportExcel: (params) => {
    return request.post('/enrollmentPlan/exportExcel', params, { responseType: 'blob' });
  },

  /**
   * @description: 下载导入模板 <AUTHOR>
   * @return {Promise} 模板文件
   */
  downloadTemplate: () => {
    return request.get('/enrollmentPlan/downloadTemplate', { responseType: 'blob' });
  },

  /**
   * @description: 导入Excel <AUTHOR>
   * @param {FormData} formData 包含文件和年度的表单数据
   * @return {Promise} 导入结果
   */
  importExcel: (formData) => {
    return request.post('/enrollmentPlan/importExcel', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },
};
